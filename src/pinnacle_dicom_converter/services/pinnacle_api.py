"""
High-level API for accessing Pinnacle data from various sources.

This module provides a unified interface for accessing Pinnacle treatment planning
data from different source types (directories, tar archives, zip files) without
needing to know the underlying storage mechanism.
"""

from pathlib import Path

from pinnacle_dicom_converter.core.models.beam import Beam
from pinnacle_dicom_converter.core.models.dose import Dose
from pinnacle_dicom_converter.core.models.image_set import ImageSet, ImageInfo
from pinnacle_dicom_converter.core.models.institution import Institution
from pinnacle_dicom_converter.core.models.machine import Machine
from pinnacle_dicom_converter.core.models.patient import Patient
from pinnacle_dicom_converter.core.models.plan import Plan
from pinnacle_dicom_converter.core.models.point import Point
from pinnacle_dicom_converter.core.models.patient_setup import PatientSetup
from pinnacle_dicom_converter.core.models.roi import ROI
from pinnacle_dicom_converter.core.models.trial import Trial
from pinnacle_dicom_converter.core.readers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
)
from pinnacle_dicom_converter.services.pinnacle_service_base import PinnacleServiceBase
from pinnacle_dicom_converter.services.pinnacle_file_service import PinnacleFileService
from pinnacle_dicom_converter.services.pinnacle_tar_service import PinnacleTarService
from pinnacle_dicom_converter.services.pinnacle_zip_service import PinnacleZipService


class PinnacleAPI:
    """
    High-level API for accessing Pinnacle data from various sources.

    Automatically detects source type (directory/tar/zip) and provides
    unified interface for all Pinnacle data operations. Supports context
    manager protocol for proper resource management.

    Supported Data Types:
        - Institution: Root-level institution information
        - Patient: Patient demographic and medical information
        - ImageSetLite: Medical imaging data sets
        - ImageInfo: Metadata about imaging data
        - Trial: Treatment trial definitions
        - ROI: Regions of interest (treatment volumes)
        - Point: Treatment planning point data
        - PatientSetup: Patient positioning information
        - Machine: Treatment delivery machine configurations
        - Dose: Dose distribution data from treatment calculations

    Usage:
        # Auto-detect source type
        api = PinnacleAPI("/path/to/data")
        institution = api.get_institution()

        # With context manager (recommended for tar/zip)
        with PinnacleAPI("/path/to/data.tar.gz") as api:
            patient = api.get_patient(institution, patient_id)

        # Explicit service injection (for testing)
        api = PinnacleAPI("/path", mock_service)
    """

    def __init__(self, source_path: str | Path, service: PinnacleServiceBase | None = None):
        """
        Initialize PinnacleAPI with automatic service type detection.

        Args:
            source_path: Path to Pinnacle data (directory, tar file, or zip file)
            service: Optional service instance for dependency injection (testing)

        Raises:
            ValueError: If source_path type is not supported or path doesn't exist
            FileNotFoundError: If the specified path does not exist
        """
        self.source_path = Path(source_path)
        self.service = service or self._create_service()

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with proper cleanup."""
        # Clean up archive-based services that need explicit cleanup
        if isinstance(self.service, (PinnacleTarService, PinnacleZipService)):
            # These services have destructors that handle cleanup
            del self.service

    def _create_service(self) -> PinnacleServiceBase:
        """
        Create appropriate service instance based on source path type.

        Returns:
            PinnacleServiceBase: Appropriate service for the source type

        Raises:
            ValueError: If source type is not supported
            FileNotFoundError: If the path does not exist
        """
        if not self.source_path.exists():
            raise FileNotFoundError(f"Path does not exist: {self.source_path}")

        if self.source_path.is_file():
            if (self.source_path.suffix in ['.tar', '.tgz'] or
                self.source_path.name.endswith('.tar.gz')):
                return PinnacleTarService(str(self.source_path))
            elif self.source_path.suffix == '.zip':
                return PinnacleZipService(self.source_path)
            else:
                raise ValueError(
                    f"Unsupported file type: {self.source_path.suffix}. "
                    f"Supported types: .tar, .tar.gz, .tgz, .zip"
                )
        elif self.source_path.is_dir():
            return PinnacleFileService(str(self.source_path))
        else:
            raise ValueError(
                f"Path must be a directory or supported archive file: {self.source_path}"
            )

    def get_institution(self) -> Institution:
        """Get institution data."""
        return InstitutionReader(self.service).read_institution()

    def get_patient(self, institution: Institution, patient_id: int) -> Patient:
        """Get patient data."""
        return PatientReader(self.service).read_patient(institution, patient_id)

    def get_image(self, institution: Institution, patient: Patient, image_set_id: int) -> ImageSet:
        """Get image data with pixel data loaded from binary file."""
        return ImageReader(self.service).read_image_with_pixel_data(institution, patient, image_set_id)

    def get_image_header(self, institution: Institution, patient: Patient, image_set_id: int) -> ImageSet:
        """Get image header."""
        return ImageReader(self.service).read_image_header(institution, patient, image_set_id)

    def get_image_info(self, institution: Institution, patient: Patient, image_set_id: int) -> list[ImageInfo]:
        """Get image info."""
        return ImageReader(self.service).read_image_info(institution, patient, image_set_id)

    def get_points(self, institution: Institution, patient: Patient, plan: Plan) -> list[Point]:
        """Get points data."""
        return PointReader(self.service).read_points(institution, patient, plan)

    def get_patient_setup(self, institution: Institution, patient: Patient, plan: Plan) -> PatientSetup:
        """Get patient setup data."""
        return PatientSetupReader(self.service).read_patient_setup(institution, patient, plan)

    def get_trials(self, institution: Institution, patient: Patient, plan: Plan) -> list[Trial]:
        """Get trials data."""
        return TrialReader(self.service).read_trials(institution, patient, plan)
    
    def get_beam_dose(self, institution: Institution, patient: Patient, plan: Plan, trial: Trial, beam: Beam) -> Dose:
        """Get beam dose data."""
        return DoseReader(self.service).read_beam_dose_data(institution, patient, plan, trial, beam)
    
    def get_trial_dose(self, institution: Institution, patient: Patient, plan: Plan, trial: Trial) -> Dose:
        """Get trial dose data."""
        return DoseReader(self.service).read_trial_dose_data(institution, patient, plan, trial)
    
    def get_rois(self, institution: Institution, patient: Patient, plan: Plan) -> list[ROI]:
        """Get ROIs data."""
        return ROIReader(self.service).read_rois(institution, patient, plan)
    
    def get_machines(self, institution: Institution, patient: Patient, plan: Plan) -> list[Machine]:
        """Get machine data."""
        return MachineReader(self.service).read_machines(institution, patient, plan)