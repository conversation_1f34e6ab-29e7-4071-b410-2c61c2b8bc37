"""CT Viewer component for central image display.

This module provides the main CT image viewer with orientation controls,
interactive image display, and navigation hints. It uses NiceGUI's
interactive_image component with SVG overlay support.
"""

from nicegui import ui

from pinnacle_dicom_converter.ui.viewmodels.image_viewmodel import ImageViewModel


def create_ct_viewer(image_vm: ImageViewModel) -> None:
    """Create the central CT image viewer.
    
    Args:
        image_vm: ImageViewModel instance for state management
    """
    with ui.column().classes("w-full h-[calc(100vh-75px)] bg-black"):
        # Title bar with orientation controls
        with ui.row().classes("w-full items-center justify-between px-4 py-2 bg-gray-900"):
            ui.label("CT Viewer").classes("text-h6 font-medium")

            # Image info (center)
            image_info_label = ui.label("No image loaded").classes("text-sm text-gray-400")
            image_info_label.bind_text_from(image_vm, "image_info_text")

            # Orientation controls (right)
            with ui.row().classes("gap-2"):
                orientation_buttons = {}
                for orientation in ["Axial", "Sagittal", "Coronal"]:
                    btn = ui.label(orientation).classes("text-sm cursor-pointer px-2 py-1 rounded")
                    btn.on("click", lambda o=orientation: image_vm.set_orientation(str(o).lower()))
                    orientation_buttons[orientation.lower()] = btn

                    if orientation != "Coronal":
                        ui.label("|").classes("text-gray-600")

                # Function to update orientation button styles
                def update_orientation_styles():
                    for orient, button in orientation_buttons.items():
                        if image_vm.current_orientation == orient:
                            button.classes("text-blue-400 bg-blue-900/30 underline", remove="text-gray-400 hover:text-gray-300")
                        else:
                            button.classes("text-gray-400 hover:text-gray-300", remove="text-blue-400 bg-blue-900/30 underline")

                # Initial styling
                update_orientation_styles()

                # Store the style update function for later use
                image_vm._update_orientation_styles = update_orientation_styles

        # Main CT image viewer
        create_interactive_image(image_vm)

        # Bottom controls bar
        with ui.row().classes("w-full items-center justify-between px-4 py-2 bg-gray-900"):
            # Navigation hints (left)
            ui.label(
                "Navigation: ↑↓ (1 slice), PgUp/PgDn (5 slices), Mouse wheel | "
                "Zoom: +/-, Home=Reset | Coordinates: Mouse position"
            ).classes("text-xs text-gray-400")

            # Coordinate display (right)
            coord_label = ui.label("").classes("text-xs text-gray-300")
            coord_label.bind_text_from(image_vm, "coordinate_text")


@ui.refreshable
def create_interactive_image(image_vm: ImageViewModel) -> None:
    """Create refreshable interactive image.

    Args:
        image_vm: ImageViewModel instance for state management
    """
    # Container for the image display area - takes remaining space
    with ui.column().classes("flex-1 w-full items-center justify-center overflow-hidden"):
        if image_vm.current_image_url is not None:
            # Create interactive image that fits within container
            img = ui.interactive_image(
                source=image_vm.current_image_url,
                on_mouse=lambda e: image_vm.on_mouse_move(e.image_x, e.image_y),
                cross=False  # No crosshair by default
            ).classes("max-w-full max-h-full object-contain")

            # Add SVG overlays if available
            if image_vm.svg_content:
                img.content = image_vm.svg_content

            # Set up keyboard event handling
            ui.keyboard(
                on_key=lambda e: image_vm.on_keyboard_event(str(e.key), str(e.action))
            )
        else:
            # Placeholder when no image loaded
            ui.icon("image", size="4rem").classes("text-gray-700")
            ui.label("Load a patient plan to view CT images").classes("text-gray-500")


def setup_refresh_callbacks(image_vm: ImageViewModel) -> None:
    """Set up refresh callbacks for the CT viewer.
    
    Args:
        image_vm: ImageViewModel instance to monitor for changes
    """
    def refresh_callback():
        """Combined callback that refreshes UI and updates orientation styles."""
        # Refresh the interactive image
        create_interactive_image.refresh()

        # Update orientation styles if the function exists
        if hasattr(image_vm, '_update_orientation_styles') and image_vm._update_orientation_styles is not None:
            image_vm._update_orientation_styles()

    # Set the callback
    image_vm.on_image_changed = refresh_callback
