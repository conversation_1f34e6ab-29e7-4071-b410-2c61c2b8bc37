"""Main application view with layout and ViewModel integration."""

from nicegui import ui

from pinnacle_dicom_converter.ui.views.header_toolbar import create_header_toolbar
from pinnacle_dicom_converter.ui.views.left_drawer import create_left_drawer, setup_refresh_callbacks as setup_nav_refresh
from pinnacle_dicom_converter.ui.views.right_drawer import create_right_drawer, setup_refresh_callbacks as setup_overlay_refresh
from pinnacle_dicom_converter.ui.views.ct_viewer import create_ct_viewer, setup_refresh_callbacks as setup_image_refresh
from pinnacle_dicom_converter.ui.viewmodels.toolbar_viewmodel import ToolbarViewModel
from pinnacle_dicom_converter.ui.viewmodels.navigation_viewmodel import NavigationViewModel
from pinnacle_dicom_converter.ui.viewmodels.image_viewmodel import ImageViewModel
from pinnacle_dicom_converter.ui.viewmodels.overlay_viewmodel import OverlayViewModel


def create_main_view() -> None:
    """Create the main application view with all components.

    This function sets up the complete MVVM architecture including:
    - ViewModels initialization and wiring
    - Header toolbar (Phase 2)
    - Left drawer navigation (Phase 3)
    - Center CT viewer (Phase 4 - Complete)
    - Right drawer controls (Phase 5)
    """
    # Initialize ViewModels
    toolbar_vm = ToolbarViewModel()
    nav_vm = NavigationViewModel()
    image_vm = ImageViewModel()
    overlay_vm = OverlayViewModel()

    # Wire up toolbar callbacks to navigation VM and overlay VM
    def on_open_data():
        """Handle data loading - loads mock data for testing."""
        nav_vm.load_mock_data()
        image_vm.load_mock_image_set()
        overlay_vm.load_mock_data()
        toolbar_vm.has_data_loaded = True
        ui.notify("Mock data loaded successfully", type="positive")

    def on_close_data():
        """Handle data closing - resets all ViewModels."""
        nav_vm.reset()
        image_vm.reset()
        overlay_vm.reset()
        toolbar_vm.has_data_loaded = False
        toolbar_vm.is_selection_complete = False
        ui.notify("Dataset closed", type="info")

    # Set up toolbar callbacks
    toolbar_vm.on_open_directory = on_open_data
    toolbar_vm.on_open_archive = on_open_data
    toolbar_vm.on_data_closed = on_close_data

    # Wire up navigation callbacks to toolbar
    def on_trial_selected(trial_id: int):
        """Handle trial selection - marks selection as complete."""
        toolbar_vm.is_selection_complete = True
        # ui.notify(f"Trial {trial_id} selected - ready for export", type="positive")

    nav_vm.on_trial_changed = on_trial_selected

    # Header toolbar with icon buttons and menus (Phase 2)
    create_header_toolbar(toolbar_vm)

    # Left drawer with navigation grids (Phase 3)
    create_left_drawer(nav_vm)
    setup_nav_refresh(nav_vm)

    # Center content area (CT Viewer - Phase 4)
    create_ct_viewer(image_vm)
    setup_image_refresh(image_vm)

    # Right drawer with control panels (Phase 5)
    create_right_drawer(nav_vm, image_vm, overlay_vm)
    setup_overlay_refresh(overlay_vm)

    # Auto-load mock data for testing (Phase 3, 4 & Phase 5)
    # This will be removed when real data loading is implemented
    nav_vm.load_mock_data()
    image_vm.load_mock_image_set()
    overlay_vm.load_mock_data()
    toolbar_vm.has_data_loaded = True