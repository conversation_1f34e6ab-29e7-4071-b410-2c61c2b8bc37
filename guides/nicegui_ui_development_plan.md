# Pinnacle DICOM Converter - NiceGUI UI Development Plan

## Overview

This plan outlines the conversion of the Pinnacle DICOM Converter UI from tkinter/ttkbootstrap to NiceGUI, following an MVVM architecture with reactive data binding. The application will be packaged as a desktop application while leveraging modern web technologies.

## Architecture

### MVVM Pattern

```
┌─────────────────────────────────────────────────────────────┐
│                          Views                              │
│  (NiceGUI UI Components with Data Bindings)                 │
│  - Header Toolbar                                           │
│  - Left Drawer (Patient/Plan/Trial AgGrids)                 │
│  - CT Viewer (interactive_image with SVG overlays)          │
│  - Right Drawer (Tab Panels for CT/ROI/POI/Beam/Dose)       │
└────────────────┬────────────────────────────────────────────┘
                 │ Data Binding
                 │ @ui.refreshable
┌────────────────▼────────────────────────────────────────────┐
│                       ViewModels                            │
│  - MainViewModel (orchestrates all child VMs)               │
│  - NavigationViewModel (patient/plan/trial selection)       │
│  - ImageViewModel (CT display state, orientation)           │
│  - OverlayViewModel (ROI/POI/Beam/Dose visibility)          │
│  - ToolbarViewModel (button states, menu actions)           │
└────────────────┬────────────────────────────────────────────┘
                 │ Service Injection
┌────────────────▼────────────────────────────────────────────┐
│                    Services & Models                        │
│  - PinnacleAPI (data provider service)                      │
│  - Display Models (simplified for UI rendering)             │
│    • PatientDisplayModel                                    │
│    • PlanDisplayModel                                       │
│    • TrialDisplayModel                                      │
│    • ROIDisplayModel                                        │
│    • DoseDisplayModel                                       │
└─────────────────────────────────────────────────────────────┘
```

### Technology Stack

- **Framework**: NiceGUI 2.24+
- **Backend**: Python 3.12+, FastAPI (NiceGUI's foundation)
- **Frontend**: Vue.js 3.4+, Quasar 2.16+ (auto-generated by NiceGUI)
- **Data Grids**: AgGrid with pandas DataFrames
- **Image Rendering**: ui.interactive_image() with SVG overlays
- **State Management**: NiceGUI app.storage + reactive binding
- **Styling**: Tailwind CSS utility classes + custom dark theme
- **Packaging**: nicegui-pack which is based on PyInstaller (native desktop executable)

## Project Structure

```
src/pinnacle_dicom_converter/
├── ui/                      # New NiceGUI UI module
│   ├── __init__.py
│   ├── main.py                      # Entry point with ui.run()
│   │
│   ├── views/                       # NiceGUI view components
│   │   ├── __init__.py
│   │   ├── header_toolbar.py        # Top toolbar with icons
│   │   ├── left_drawer.py           # Patient/Plan/Trial navigation
│   │   ├── ct_viewer.py             # Central CT image viewer
│   │   ├── right_drawer.py          # Tab panels container
│   │   └── panels/                  # Right drawer tab panels
│   │       ├── ct_panel.py          # CT controls (slice, window/level, zoom)
│   │       ├── roi_panel.py         # ROI visibility/color controls
│   │       ├── poi_panel.py         # POI visibility/color controls
│   │       ├── beam_panel.py        # Beam visualization controls
│   │       └── dose_panel.py        # Dose display controls
│   │
│   ├── viewmodels/                  # MVVM ViewModels
│   │   ├── __init__.py
│   │   ├── main_viewmodel.py        # Orchestrates all VMs
│   │   ├── navigation_viewmodel.py  # Patient/plan/trial selection
│   │   ├── image_viewmodel.py       # CT display state
│   │   ├── overlay_viewmodel.py     # ROI/POI/Beam/Dose state
│   │   └── toolbar_viewmodel.py     # Toolbar button states
│   │
│   ├── models/                      # Display models
│   │   ├── __init__.py
│   │   ├── patient_display.py       # Simplified patient model
│   │   ├── plan_display.py          # Simplified plan model
│   │   ├── trial_display.py         # Simplified trial model
│   │   ├── roi_display.py           # ROI with color/visibility
│   │   ├── poi_display.py           # POI with marker style
│   │   ├── beam_display.py          # Beam visualization data
│   │   └── dose_display.py          # Dose grid display data
│   │
│   ├── converters/                  # Data converters
│   │   ├── __init__.py
│   │   ├── svg_converter.py         # Convert geometry to SVG
│   │   ├── roi_to_svg.py            # ROI contours → SVG paths
│   │   ├── poi_to_svg.py            # POIs → SVG markers
│   │   ├── beam_to_svg.py           # Beam lines → SVG
│   │   └── isodose_to_svg.py        # Isodose lines → SVG paths
│   │
│   ├── services/                    # Service layer
│   │   ├── __init__.py
│   │   ├── pinnacle_service.py      # Wraps PinnacleAPI
│   │   ├── image_service.py         # CT image preparation
│   │   └── storage_service.py       # App storage management
│   │
│   ├── styles/                      # Styling resources
│   │   ├── __init__.py
│   │   ├── theme.py                 # Dark theme configuration
│   │   └── custom.css               # Additional custom styles
│   │
│   └── utils/                       # Utilities
│       ├── __init__.py
│       ├── keyboard_shortcuts.py    # Keyboard event handlers
│       └── dialogs.py               # Reusable dialog components
│
└── ui/                              # Old tkinter UI (keep for reference)
    └── ...
```

## Development Phases

### Phase 1: Foundation & Infrastructure (Days 1-2)

**Objective**: Set up project structure, dark theme, and basic application shell

#### Task 1.1: Project Setup
- Create `ui/` directory structure
- Set up `main.py` with NiceGUI initialization
- Configure dark theme using Quasar dark mode
- Implement basic app.storage configuration

**Deliverable**: Running NiceGUI app with dark theme

#### Task 1.2: Dark Theme Implementation
- Define color palette matching tkinter "darkly" theme
- Create `styles/theme.py` with color configuration
- Apply global styles using `ui.colors()` and `ui.add_head_html()`
- Test dark mode persistence across sessions

**Deliverable**: Fully styled dark theme matching reference screenshots

**Reference Files**:
- `/home/<USER>/source/repos/tools/nicegui/dev-guides/06-styling-theming.md`
- https://nicegui.io/documentation/dark_mode

---

### Phase 2: Header Toolbar (Day 2)

**Objective**: Implement the top toolbar with icon buttons and menus

#### Task 2.1: Create Toolbar View
- Implement `views/header_toolbar.py`
- Create toolbar layout: title left, icons right
- Style with Tailwind classes for professional appearance

**Component Specification**:
```python
# views/header_toolbar.py
from nicegui import ui
from ..viewmodels.toolbar_viewmodel import ToolbarViewModel

def create_header_toolbar(toolbar_vm: ToolbarViewModel):
    """Create the header toolbar with icon buttons"""
    with ui.header().classes('items-center justify-between px-6 shadow-md'):
        # Left: Application title
        ui.label('Pinnacle DICOM Converter').classes('text-h6 font-medium')

        # Right: Icon toolbar
        with ui.row().classes('items-center gap-1'):
            # File menu (folder icon)
            with ui.button(icon='folder_open', color='white').props('flat round') as open_btn:
                open_btn.tooltip('Open Pinnacle Dataset')
                with ui.menu():
                    ui.menu_item('Open Directory...',
                                on_click=lambda: toolbar_vm.open_directory())
                    ui.menu_item('Open Archive (.tar, .tar.gz, .zip)...',
                                on_click=lambda: toolbar_vm.open_archive())
                    ui.separator()
                    close_item = ui.menu_item('Close Dataset',
                                             on_click=lambda: toolbar_vm.close_dataset())
                    # Bind enabled state to ViewModel
                    close_item.bind_enabled_from(toolbar_vm, 'has_data_loaded')

            ui.separator().props('vertical inset').classes('mx-2')

            # Download button (save locally)
            download_btn = ui.button(icon='download', color='white',
                                    on_click=lambda: toolbar_vm.save_dicom_local()
                                    ).props('flat round')
            download_btn.tooltip('Save DICOM Files Locally')
            download_btn.bind_enabled_from(toolbar_vm, 'is_selection_complete')

            # Export button (network)
            export_btn = ui.button(icon='cloud_upload', color='white',
                                  on_click=lambda: toolbar_vm.export_dicom_network()
                                  ).props('flat round')
            export_btn.tooltip('Export DICOM to Network')
            export_btn.bind_enabled_from(toolbar_vm, 'is_selection_complete')

            ui.separator().props('vertical inset').classes('mx-2')

            # Help button
            ui.button(icon='help_outline', color='white',
                     on_click=lambda: toolbar_vm.show_help()
                     ).props('flat round').tooltip('Help & About')
```

#### Task 2.2: Create Toolbar ViewModel
- Implement `viewmodels/toolbar_viewmodel.py`
- Add properties: `has_data_loaded`, `is_selection_complete`
- Implement methods: `open_directory()`, `open_archive()`, `close_dataset()`, etc.
- Use native file dialogs (via local_file_picker for desktop mode)

**Deliverable**: Functional toolbar with enabled/disabled state management

**Reference Files**:
- https://nicegui.io/documentation/menu
- https://nicegui.io/documentation/button

---

### Phase 3: Left Drawer - Navigation (Days 3-4)

**Objective**: Implement patient, plan, and trial selection using AgGrid

#### Task 3.1: Create Left Drawer View
- Implement `views/left_drawer.py`
- Create three AgGrid components (Patients, Plans, Trials)
- Style grids with dark theme
- Configure grid selection behavior (single row selection)

**Component Specification**:
```python
# views/left_drawer.py
from nicegui import ui
import pandas as pd
from ..viewmodels.navigation_viewmodel import NavigationViewModel

def create_left_drawer(nav_vm: NavigationViewModel):
    """Create left navigation drawer with patient/plan/trial grids"""
    with ui.left_drawer(fixed=True).classes('bg-gray-900 w-96') as drawer:
        drawer.show()  # Always visible in desktop mode

        with ui.column().classes('w-full h-full gap-4 p-4'):
            # Section header
            ui.label('Navigation').classes('text-h6 font-medium mb-2')

            # Patients AgGrid
            ui.label('Patients').classes('text-subtitle1 font-medium')
            patients_grid = create_patients_grid(nav_vm)

            # Plans AgGrid
            ui.label('Plans').classes('text-subtitle1 font-medium')
            plans_grid = create_plans_grid(nav_vm)

            # Trials AgGrid
            ui.label('Trials').classes('text-subtitle1 font-medium')
            trials_grid = create_trials_grid(nav_vm)

@ui.refreshable
def create_patients_grid(nav_vm: NavigationViewModel):
    """Create refreshable patients AgGrid"""
    if nav_vm.patients_df is not None and not nav_vm.patients_df.empty:
        grid = ui.aggrid({
            'columnDefs': [
                {'field': 'id', 'headerName': 'ID', 'width': 60},
                {'field': 'mrn', 'headerName': 'MRN', 'width': 100},
                {'field': 'name', 'headerName': 'Name', 'width': 180}
            ],
            'rowData': nav_vm.patients_df.to_dict('records'),
            'rowSelection': 'single',
            'theme': 'quartz-dark',  # Dark theme for AgGrid
            'domLayout': 'autoHeight'
        }).classes('w-full')

        grid.on('selectionChanged', lambda e: nav_vm.on_patient_selected(e.args))
        return grid
    else:
        ui.label('No patients loaded').classes('text-gray-500 text-center py-4')

# Similar implementations for create_plans_grid() and create_trials_grid()
```

#### Task 3.2: Create Navigation ViewModel
- Implement `viewmodels/navigation_viewmodel.py`
- Maintain pandas DataFrames for patients, plans, trials
- Implement selection handlers that trigger data loading
- Update DataFrames when parent selections change

**ViewModel Specification**:
```python
# viewmodels/navigation_viewmodel.py
from dataclasses import dataclass, field
import pandas as pd
from typing import Optional
from ..services.pinnacle_service import PinnacleService

@dataclass
class NavigationViewModel:
    """ViewModel for patient/plan/trial navigation"""

    # Injected service
    pinnacle_service: PinnacleService

    # DataFrames for AgGrid (reactive)
    patients_df: Optional[pd.DataFrame] = None
    plans_df: Optional[pd.DataFrame] = None
    trials_df: Optional[pd.DataFrame] = None

    # Current selections
    selected_patient_id: Optional[int] = None
    selected_plan_id: Optional[int] = None
    selected_trial_id: Optional[int] = None

    # Callbacks to notify other ViewModels
    on_patient_changed: Optional[callable] = None
    on_plan_changed: Optional[callable] = None
    on_trial_changed: Optional[callable] = None

    def load_patients(self):
        """Load patients from PinnacleAPI and update DataFrame"""
        institution = self.pinnacle_service.get_institution()

        # Convert to DataFrame for AgGrid
        patient_data = [
            {
                'id': p.patient_id,
                'mrn': p.medical_record_number,
                'name': f"{p.last_name}, {p.first_name}"
            }
            for p in institution.patient_lite_list
        ]

        self.patients_df = pd.DataFrame(patient_data)

        # Trigger UI refresh
        create_patients_grid.refresh()

    def on_patient_selected(self, selection_event):
        """Handle patient selection from AgGrid"""
        if selection_event and len(selection_event) > 0:
            selected_row = selection_event[0]
            self.selected_patient_id = selected_row['id']

            # Load plans for selected patient
            self.load_plans()

            # Notify other ViewModels
            if self.on_patient_changed:
                self.on_patient_changed(self.selected_patient_id)

    def load_plans(self):
        """Load plans for selected patient"""
        if self.selected_patient_id is None:
            return

        patient = self.pinnacle_service.get_patient(
            institution=1,
            patient=self.selected_patient_id
        )

        plan_data = [
            {
                'id': plan.plan_id,
                'name': plan.name
            }
            for plan in patient.plan_list
        ]

        self.plans_df = pd.DataFrame(plan_data)
        create_plans_grid.refresh()

    # Similar methods: on_plan_selected(), load_trials(), on_trial_selected()
```

#### Task 3.3: Display Models
- Create `models/patient_display.py`, `plan_display.py`, `trial_display.py`
- Define simplified data classes for UI rendering
- Include only fields needed for display

**Deliverable**: Functional left drawer with cascading selection behavior

**Reference Files**:
- https://nicegui.io/documentation/aggrid
- https://www.ag-grid.com/vue-data-grid/themes/ (use `quartz-dark` theme)

---

### Phase 4: CT Viewer - Central Image Display (Days 5-7)

**Objective**: Implement the CT image viewer with orientation controls and SVG overlay support

#### Task 4.1: Create CT Viewer View
- Implement `views/ct_viewer.py`
- Use `ui.interactive_image()` for CT display
- Add orientation toggle (Axial | Sagittal | Coronal)
- Display image information at top
- Show navigation hints at bottom

**Component Specification**:
```python
# views/ct_viewer.py
from nicegui import ui
from pathlib import Path
from ..viewmodels.image_viewmodel import ImageViewModel

def create_ct_viewer(image_vm: ImageViewModel):
    """Create the central CT image viewer"""
    with ui.column().classes('flex-1 bg-black'):
        # Title bar with orientation controls
        with ui.row().classes('w-full items-center justify-between px-4 py-2 bg-gray-900'):
            ui.label('CT Viewer').classes('text-h6 font-medium')

            # Image info (center)
            image_info_label = ui.label('No image loaded').classes('text-sm text-gray-400')
            image_info_label.bind_text_from(image_vm, 'image_info_text')

            # Orientation controls (right)
            with ui.row().classes('gap-2'):
                for orientation in ['Axial', 'Sagittal', 'Coronal']:
                    btn = ui.label(orientation).classes('text-sm cursor-pointer px-2')
                    btn.on('click', lambda o=orientation: image_vm.set_orientation(o.lower()))
                    # Style active orientation
                    btn.bind_classes_from(
                        image_vm,
                        'current_orientation',
                        lambda val, o=orientation: 'text-gray-500 underline' if val == o.lower() else 'text-blue-400'
                    )
                    if orientation != 'Coronal':
                        ui.label('|').classes('text-gray-600')

        # Main CT image viewer
        create_interactive_image(image_vm)

        # Bottom controls bar
        with ui.row().classes('w-full items-center justify-between px-4 py-2 bg-gray-900'):
            # Navigation hints (left)
            ui.label('Navigation: ↑↓ (1 slice), PgUp/PgDn (5 slices), Mouse wheel | Zoom: Ctrl+Wheel, +/-, Home=Reset'
                    ).classes('text-xs text-gray-400')

            # Coordinate display (right)
            coord_label = ui.label('').classes('text-xs text-gray-300')
            coord_label.bind_text_from(image_vm, 'coordinate_text')

@ui.refreshable
def create_interactive_image(image_vm: ImageViewModel):
    """Create refreshable interactive image"""
    if image_vm.current_image_url is not None:
        # Create interactive image
        img = ui.interactive_image(
            source=image_vm.current_image_url,
            on_mouse=lambda e: image_vm.on_mouse_move(e.image_x, e.image_y),
            cross=False  # No crosshair by default
        ).classes('w-full h-full')

        # Add SVG overlays
        if image_vm.svg_content:
            img.content = image_vm.svg_content

        # Keyboard event handling
        ui.keyboard(
            on_key=lambda e: image_vm.on_keyboard_event(e.key, e.action)
        )

        return img
    else:
        # Placeholder when no image loaded
        with ui.column().classes('w-full h-full items-center justify-center'):
            ui.icon('image', size='4rem').classes('text-gray-700')
            ui.label('Load a patient plan to view CT images').classes('text-gray-500')
```

#### Task 4.2: Create Image ViewModel
- Implement `viewmodels/image_viewmodel.py`
- Manage CT image state (current slice, orientation, zoom, window/level)
- Handle keyboard shortcuts
- Generate image URL from numpy array
- Prepare SVG overlay content

**ViewModel Specification**:
```python
# viewmodels/image_viewmodel.py
from dataclasses import dataclass, field
from typing import Optional
import numpy as np
import base64
from io import BytesIO
from PIL import Image

@dataclass
class ImageViewModel:
    """ViewModel for CT image display"""

    # Image data
    image_set: Optional[object] = None  # ImageSet from PinnacleAPI
    current_slice_index: int = 0
    total_slices: int = 0

    # Display parameters
    current_orientation: str = 'axial'  # 'axial', 'sagittal', 'coronal'
    window_width: int = 1400
    window_level: int = 1000
    zoom_factor: float = 1.0

    # Image URL for ui.interactive_image
    current_image_url: Optional[str] = None

    # SVG overlay content
    svg_content: str = ''

    # Display text
    image_info_text: str = 'No image loaded'
    coordinate_text: str = ''

    # Coordinate tracking
    pixel_spacing: tuple = (1.0, 1.0, 1.0)
    image_origin: tuple = (0.0, 0.0, 0.0)

    def load_image_set(self, image_set):
        """Load CT image set"""
        self.image_set = image_set

        if image_set and hasattr(image_set, 'pixel_data'):
            dimensions = image_set.get_image_dimensions()
            self.total_slices = dimensions[2] if dimensions else 0
            self.current_slice_index = self.total_slices // 2
            self.pixel_spacing = image_set.get_pixel_spacing()

            # Update info text
            self.image_info_text = (
                f"CT - {dimensions[0]}×{dimensions[1]}×{dimensions[2]} - "
                f"Spacing: {self.pixel_spacing[0]:.2f}×{self.pixel_spacing[1]:.2f}×{self.pixel_spacing[2]:.2f}mm"
            )

            # Generate initial image
            self.update_image_display()

    def update_image_display(self):
        """Update the displayed image based on current parameters"""
        if not self.image_set:
            return

        # Get slice data for current orientation
        slice_data = self._get_slice_data()

        # Apply window/level
        windowed_data = self._apply_window_level(slice_data)

        # Convert to PIL Image
        pil_image = Image.fromarray(windowed_data, mode='L')

        # Convert to base64 data URL
        buffer = BytesIO()
        pil_image.save(buffer, format='PNG')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode()
        self.current_image_url = f'data:image/png;base64,{image_base64}'

        # Refresh the UI
        create_interactive_image.refresh()

    def set_orientation(self, orientation: str):
        """Change view orientation"""
        if self.current_orientation != orientation:
            self.current_orientation = orientation
            self.update_image_display()

    def navigate_slice(self, delta: int):
        """Navigate to different slice by delta"""
        new_slice = max(0, min(self.current_slice_index + delta, self.total_slices - 1))
        if new_slice != self.current_slice_index:
            self.current_slice_index = new_slice
            self.update_image_display()

    def on_keyboard_event(self, key: str, action: str):
        """Handle keyboard shortcuts"""
        if action != 'down':
            return

        if key == 'ArrowUp':
            self.navigate_slice(-1)
        elif key == 'ArrowDown':
            self.navigate_slice(1)
        elif key == 'PageUp':
            self.navigate_slice(-5)
        elif key == 'PageDown':
            self.navigate_slice(5)
        elif key == 'Home':
            self.zoom_factor = 1.0
            self.update_image_display()
        # Additional shortcuts...

    def on_mouse_move(self, image_x: float, image_y: float):
        """Update coordinate display on mouse move"""
        world_coords = self._pixel_to_world(image_x, image_y)
        if world_coords:
            x, y, z = world_coords
            self.coordinate_text = f"({x:.3f}, {y:.3f}, {z:.3f})"

    # Helper methods: _get_slice_data(), _apply_window_level(), _pixel_to_world()
```

#### Task 4.3: Image Service
- Create `ui/services/image_service.py`
- Handle image data conversion (numpy → PIL → base64)
- Optimize for performance (consider caching rendered slices)
- Handle different orientations (axial, sagittal, coronal)

**Deliverable**: Functional CT viewer with orientation switching and keyboard navigation

**Reference Files**:
- https://nicegui.io/documentation/interactive_image
- `/home/<USER>/source/repos/tools/nicegui/nicegui/elements/interactive_image.py`

---

### Phase 5: Right Drawer - Control Panels (Days 8-10)

**Objective**: Implement the right drawer with tabbed panels for CT controls, ROIs, POIs, Beams, and Dose

#### Task 5.1: Create Right Drawer Container
- Implement `views/right_drawer.py`
- Create tab system for different panels
- Style tabs to match dark theme

**Component Specification**:
```python
# views/right_drawer.py
from nicegui import ui
from ..viewmodels.main_viewmodel import MainViewModel
from .panels import ct_panel, roi_panel, poi_panel, beam_panel, dose_panel

def create_right_drawer(main_vm: MainViewModel):
    """Create right drawer with control panels"""
    with ui.right_drawer(fixed=True).classes('bg-gray-900 w-96') as drawer:
        drawer.show()  # Always visible

        with ui.column().classes('w-full h-full'):
            # Current selection info
            with ui.card().classes('w-full bg-gray-800 mb-4'):
                ui.label('Current Selection').classes('text-subtitle1 font-medium mb-2')

                patient_label = ui.label('Patient: -').classes('text-sm')
                patient_label.bind_text_from(
                    main_vm.navigation_vm,
                    'selected_patient_id',
                    backward=lambda id: f'Patient: {id}' if id else 'Patient: -'
                )

                plan_label = ui.label('Plan: -').classes('text-sm')
                plan_label.bind_text_from(
                    main_vm.navigation_vm,
                    'selected_plan_id',
                    backward=lambda id: f'Plan: {id}' if id else 'Plan: -'
                )

                trial_label = ui.label('Trial: -').classes('text-sm')
                trial_label.bind_text_from(
                    main_vm.navigation_vm,
                    'selected_trial_id',
                    backward=lambda id: f'Trial: {id}' if id else 'Trial: -'
                )

            # Tabbed panels
            with ui.tabs().classes('w-full') as tabs:
                ct_tab = ui.tab('CT', icon='image')
                roi_tab = ui.tab('ROIs', icon='polyline')
                poi_tab = ui.tab('POIs', icon='place')
                beam_tab = ui.tab('Beams', icon='straighten')
                dose_tab = ui.tab('Dose', icon='gradient')

            with ui.tab_panels(tabs, value=ct_tab).classes('w-full flex-1'):
                with ui.tab_panel(ct_tab):
                    ct_panel.create_ct_panel(main_vm.image_vm)

                with ui.tab_panel(roi_tab):
                    roi_panel.create_roi_panel(main_vm.overlay_vm)

                with ui.tab_panel(poi_tab):
                    poi_panel.create_poi_panel(main_vm.overlay_vm)

                with ui.tab_panel(beam_tab):
                    beam_panel.create_beam_panel(main_vm.overlay_vm)

                with ui.tab_panel(dose_tab):
                    dose_panel.create_dose_panel(main_vm.overlay_vm)
```

#### Task 5.2: CT Control Panel
- Implement `views/panels/ct_panel.py`
- Add slice control (slider + input field)
- Add window/level controls (sliders + input fields)
- Add zoom control (slider + input field)
- Bind all controls to ImageViewModel

**Component Specification**:
```python
# views/panels/ct_panel.py
from nicegui import ui
from ...viewmodels.image_viewmodel import ImageViewModel

def create_ct_panel(image_vm: ImageViewModel):
    """Create CT control panel"""
    with ui.column().classes('w-full gap-4 p-4'):
        ui.label('CT Image Controls').classes('text-h6 font-medium mb-2')

        # Image information
        with ui.card().classes('w-full bg-gray-800 p-3'):
            ui.label('Image Information').classes('text-subtitle2 font-medium mb-2')

            info_text = ui.label('').classes('text-xs text-gray-300')
            info_text.bind_text_from(image_vm, 'image_info_text')

        # Slice control
        with ui.card().classes('w-full bg-gray-800 p-3'):
            ui.label('Slice').classes('text-subtitle2 font-medium mb-2')

            with ui.row().classes('w-full items-center gap-2'):
                slice_slider = ui.slider(
                    min=0,
                    max=100,  # Will be updated dynamically
                    step=1
                ).classes('flex-1')
                slice_slider.bind_value(image_vm, 'current_slice_index')
                slice_slider.on('update:model-value', lambda: image_vm.update_image_display())

                slice_input = ui.number(
                    min=0,
                    max=100,
                    step=1,
                    value=0
                ).classes('w-20')
                slice_input.bind_value(image_vm, 'current_slice_index')

            # Slice info
            slice_info = ui.label('').classes('text-xs text-gray-400 mt-1')
            slice_info.bind_text_from(
                image_vm,
                'current_slice_index',
                backward=lambda idx: f'Slice: {idx + 1} / {image_vm.total_slices}'
            )

        # Window Width control
        with ui.card().classes('w-full bg-gray-800 p-3'):
            ui.label('Window Width').classes('text-subtitle2 font-medium mb-2')

            with ui.row().classes('w-full items-center gap-2'):
                ww_slider = ui.slider(
                    min=1,
                    max=4000,
                    step=10,
                    value=1400
                ).classes('flex-1')
                ww_slider.bind_value(image_vm, 'window_width')
                ww_slider.on('update:model-value', lambda: image_vm.update_image_display())

                ww_input = ui.number(
                    min=1,
                    max=4000,
                    step=10,
                    value=1400
                ).classes('w-24')
                ww_input.bind_value(image_vm, 'window_width')

        # Window Level control
        with ui.card().classes('w-full bg-gray-800 p-3'):
            ui.label('Window Level').classes('text-subtitle2 font-medium mb-2')

            with ui.row().classes('w-full items-center gap-2'):
                wl_slider = ui.slider(
                    min=-1024,
                    max=3071,
                    step=10,
                    value=1000
                ).classes('flex-1')
                wl_slider.bind_value(image_vm, 'window_level')
                wl_slider.on('update:model-value', lambda: image_vm.update_image_display())

                wl_input = ui.number(
                    min=-1024,
                    max=3071,
                    step=10,
                    value=1000
                ).classes('w-24')
                wl_input.bind_value(image_vm, 'window_level')

        # Zoom control
        with ui.card().classes('w-full bg-gray-800 p-3'):
            ui.label('Zoom').classes('text-subtitle2 font-medium mb-2')

            with ui.row().classes('w-full items-center gap-2'):
                zoom_slider = ui.slider(
                    min=0.1,
                    max=10.0,
                    step=0.1,
                    value=1.0
                ).classes('flex-1')
                zoom_slider.bind_value(image_vm, 'zoom_factor')
                zoom_slider.on('update:model-value', lambda: image_vm.update_image_display())

                zoom_input = ui.number(
                    min=0.1,
                    max=10.0,
                    step=0.1,
                    value=1.0,
                    format='%.1fx'
                ).classes('w-24')
                zoom_input.bind_value(image_vm, 'zoom_factor')

                # Reset button
                ui.button('Reset',
                         on_click=lambda: setattr(image_vm, 'zoom_factor', 1.0)
                         ).props('flat dense').classes('ml-2')
```

#### Task 5.3: ROI Control Panel
- Implement `views/panels/roi_panel.py`
- Create AgGrid displaying ROI list with checkboxes for visibility
- Add color pickers for each ROI
- Bind to OverlayViewModel

#### Task 5.4: POI Control Panel
- Implement `views/panels/poi_panel.py`
- Similar to ROI panel but for points of interest
- Include marker style selection

#### Task 5.5: Beam Control Panel
- Implement `views/panels/beam_panel.py`
- Display beam list with visibility toggles
- Show beam parameters (angles, energy, etc.)

#### Task 5.6: Dose Control Panel
- Implement `views/panels/dose_panel.py`
- Display isodose lines with visibility checkboxes
- Add dose reference input (cGy)
- Show dose grid information

**Deliverable**: Fully functional right drawer with all control panels

**Reference Files**:
- https://nicegui.io/documentation/tabs
- https://nicegui.io/documentation/slider
- https://nicegui.io/documentation/color_picker

---

### Phase 6: SVG Overlay System (Days 11-13)

**Objective**: Convert ROIs, POIs, beams, and isodose lines to SVG and render on CT viewer

#### Task 6.1: SVG Converter Base
- Implement `converters/svg_converter.py`
- Create base SVG conversion utilities
- Handle coordinate transformations (world → pixel)
- Apply zoom and orientation transformations

**Converter Specification**:
```python
# converters/svg_converter.py
from typing import List, Tuple
import numpy as np

class SVGConverter:
    """Base class for converting geometry to SVG"""

    def __init__(self, pixel_spacing: tuple, image_origin: tuple,
                 image_dimensions: tuple, zoom_factor: float = 1.0):
        self.pixel_spacing = pixel_spacing
        self.image_origin = image_origin
        self.image_dimensions = image_dimensions
        self.zoom_factor = zoom_factor

    def world_to_pixel(self, world_coords: Tuple[float, float, float]) -> Tuple[float, float]:
        """Convert world coordinates (mm) to pixel coordinates"""
        x, y, z = world_coords

        # Calculate pixel position
        pixel_x = (x - self.image_origin[0]) / self.pixel_spacing[0]
        pixel_y = (y - self.image_origin[1]) / self.pixel_spacing[1]

        return (pixel_x, pixel_y)

    def create_svg_header(self, width: int, height: int) -> str:
        """Create SVG header with viewBox"""
        return f'<svg width="{width}" height="{height}" viewBox="0 0 {width} {height}" xmlns="http://www.w3.org/2000/svg">'

    def create_svg_footer(self) -> str:
        """Create SVG footer"""
        return '</svg>'

    def create_svg_group(self, group_id: str, visible: bool = True) -> str:
        """Create SVG group element"""
        visibility = 'visible' if visible else 'hidden'
        return f'<g id="{group_id}" visibility="{visibility}">'

    def close_svg_group(self) -> str:
        """Close SVG group"""
        return '</g>'
```

#### Task 6.2: ROI to SVG Converter
- Implement `converters/roi_to_svg.py`
- Convert ROI contour points to SVG path elements
- Apply ROI color and visibility settings
- Handle multiple contours per slice

**Converter Specification**:
```python
# converters/roi_to_svg.py
from typing import List
from .svg_converter import SVGConverter

class ROIToSVGConverter(SVGConverter):
    """Convert ROI contours to SVG paths"""

    def convert_roi_to_svg(self, roi_display_model, current_slice_z: float) -> str:
        """Convert ROI to SVG path for current slice"""
        if not roi_display_model.visible:
            return ''

        # Get contours for current slice (within tolerance)
        contours = self._get_contours_for_slice(roi_display_model.contours, current_slice_z)

        if not contours:
            return ''

        svg_parts = []
        svg_parts.append(self.create_svg_group(f'roi_{roi_display_model.roi_id}'))

        for contour in contours:
            path_data = self._contour_to_path(contour)
            svg_parts.append(
                f'<path d="{path_data}" '
                f'fill="none" '
                f'stroke="{roi_display_model.color}" '
                f'stroke-width="2" '
                f'stroke-linejoin="round" />'
            )

        svg_parts.append(self.close_svg_group())

        return ''.join(svg_parts)

    def _contour_to_path(self, contour_points: List[Tuple[float, float, float]]) -> str:
        """Convert contour points to SVG path data"""
        if not contour_points:
            return ''

        path_commands = []

        # Move to first point
        first_pixel = self.world_to_pixel(contour_points[0])
        path_commands.append(f'M {first_pixel[0]:.2f} {first_pixel[1]:.2f}')

        # Line to subsequent points
        for point in contour_points[1:]:
            pixel = self.world_to_pixel(point)
            path_commands.append(f'L {pixel[0]:.2f} {pixel[1]:.2f}')

        # Close path
        path_commands.append('Z')

        return ' '.join(path_commands)

    def _get_contours_for_slice(self, all_contours, slice_z: float, tolerance: float = 0.5) -> List:
        """Filter contours that belong to current slice"""
        slice_contours = []

        for contour in all_contours:
            # Check if contour's z coordinate is within tolerance of slice_z
            if contour and len(contour) > 0:
                contour_z = contour[0][2]  # Z coordinate of first point
                if abs(contour_z - slice_z) < tolerance:
                    slice_contours.append(contour)

        return slice_contours
```

#### Task 6.3: POI to SVG Converter
- Implement `converters/poi_to_svg.py`
- Convert POI coordinates to SVG markers (circles, crosses, etc.)
- Apply POI color and visibility

#### Task 6.4: Beam to SVG Converter
- Implement `converters/beam_to_svg.py`
- Convert beam lines to SVG line elements
- Show beam direction with arrows
- Apply beam color and visibility

#### Task 6.5: Isodose to SVG Converter
- Implement `converters/isodose_to_svg.py`
- Convert isodose contours to SVG paths
- Apply dose-level specific colors
- Support multiple isodose levels

#### Task 6.6: Integrate Overlays into CT Viewer
- Update ImageViewModel to generate combined SVG content
- Call appropriate converters based on OverlayViewModel state
- Update SVG content when overlay settings change

**Integration Example**:
```python
# In ImageViewModel
def update_svg_overlays(self, overlay_vm):
    """Generate SVG overlay content from all sources"""
    if not self.image_set:
        self.svg_content = ''
        return

    current_z = self.get_current_slice_z()

    svg_parts = []
    svg_parts.append(self.svg_converter.create_svg_header(
        self.image_dimensions[0],
        self.image_dimensions[1]
    ))

    # Add ROI overlays
    for roi in overlay_vm.visible_rois:
        roi_svg = self.roi_converter.convert_roi_to_svg(roi, current_z)
        svg_parts.append(roi_svg)

    # Add POI overlays
    for poi in overlay_vm.visible_pois:
        poi_svg = self.poi_converter.convert_poi_to_svg(poi, current_z)
        svg_parts.append(poi_svg)

    # Add beam overlays
    for beam in overlay_vm.visible_beams:
        beam_svg = self.beam_converter.convert_beam_to_svg(beam, current_z)
        svg_parts.append(beam_svg)

    # Add isodose overlays
    for isodose in overlay_vm.visible_isodose_lines:
        isodose_svg = self.isodose_converter.convert_isodose_to_svg(isodose, current_z)
        svg_parts.append(isodose_svg)

    svg_parts.append(self.svg_converter.create_svg_footer())

    self.svg_content = ''.join(svg_parts)
    create_interactive_image.refresh()
```

**Deliverable**: Working SVG overlay system displaying ROIs, POIs, beams, and isodose lines on CT images

**Reference Files**:
- https://developer.mozilla.org/en-US/docs/Web/SVG/Tutorial
- `/home/<USER>/source/repos/arrow/python/pinnacle_db/src/pinnacle_io/ui/widgets/ct_viewer.py` (reference for coordinate transformations)

---

### Phase 7: Overlay Control ViewModel (Day 14)

**Objective**: Implement ViewModel for managing overlay visibility, colors, and state

#### Task 7.1: Create Overlay ViewModel
- Implement `viewmodels/overlay_viewmodel.py`
- Manage lists of ROIs, POIs, beams, isodose lines with display properties
- Provide methods to toggle visibility, change colors
- Notify ImageViewModel when overlay state changes

**ViewModel Specification**:
```python
# viewmodels/overlay_viewmodel.py
from dataclasses import dataclass, field
from typing import List, Optional
from ..models.roi_display import ROIDisplayModel
from ..models.poi_display import POIDisplayModel
from ..models.beam_display import BeamDisplayModel
from ..models.dose_display import IsodoseDisplayModel

@dataclass
class OverlayViewModel:
    """ViewModel for managing ROI/POI/Beam/Dose overlays"""

    # Lists of display models
    rois: List[ROIDisplayModel] = field(default_factory=list)
    pois: List[POIDisplayModel] = field(default_factory=list)
    beams: List[BeamDisplayModel] = field(default_factory=list)
    isodose_lines: List[IsodoseDisplayModel] = field(default_factory=list)

    # Dose reference value
    dose_reference_cgy: float = 3000.0

    # Callbacks
    on_overlay_changed: Optional[callable] = None

    @property
    def visible_rois(self) -> List[ROIDisplayModel]:
        """Get list of visible ROIs"""
        return [roi for roi in self.rois if roi.visible]

    @property
    def visible_pois(self) -> List[POIDisplayModel]:
        """Get list of visible POIs"""
        return [poi for poi in self.pois if poi.visible]

    @property
    def visible_beams(self) -> List[BeamDisplayModel]:
        """Get list of visible beams"""
        return [beam for beam in self.beams if beam.visible]

    @property
    def visible_isodose_lines(self) -> List[IsodoseDisplayModel]:
        """Get list of visible isodose lines"""
        return [iso for iso in self.isodose_lines if iso.visible]

    def load_rois(self, roi_data_list):
        """Load ROI data and convert to display models"""
        self.rois = [
            ROIDisplayModel(
                roi_id=roi.roi_id,
                name=roi.name,
                color=self._assign_default_color(idx),
                visible=True,
                contours=roi.contour_list  # List of contour point lists
            )
            for idx, roi in enumerate(roi_data_list)
        ]
        self._notify_change()

    def toggle_roi_visibility(self, roi_id: int):
        """Toggle ROI visibility"""
        for roi in self.rois:
            if roi.roi_id == roi_id:
                roi.visible = not roi.visible
                self._notify_change()
                break

    def change_roi_color(self, roi_id: int, new_color: str):
        """Change ROI color"""
        for roi in self.rois:
            if roi.roi_id == roi_id:
                roi.color = new_color
                self._notify_change()
                break

    def _notify_change(self):
        """Notify listeners that overlay state changed"""
        if self.on_overlay_changed:
            self.on_overlay_changed()

    def _assign_default_color(self, index: int) -> str:
        """Assign default color from palette"""
        color_palette = [
            '#FF0000', '#00FF00', '#0000FF', '#FFFF00',
            '#FF00FF', '#00FFFF', '#FFA500', '#800080'
        ]
        return color_palette[index % len(color_palette)]

    # Similar methods for POIs, beams, isodose lines
```

**Deliverable**: Fully functional overlay control system with ViewModel

---

### Phase 8: MVVM Integration & Data Flow (Days 15-16)

**Objective**: Wire together all ViewModels and establish reactive data flow

#### Task 8.1: Create Main ViewModel
- Implement `viewmodels/main_viewmodel.py`
- Orchestrate all child ViewModels
- Set up callbacks and data flow between ViewModels
- Inject PinnacleService

**Main ViewModel Specification**:
```python
# viewmodels/main_viewmodel.py
from dataclasses import dataclass
from .navigation_viewmodel import NavigationViewModel
from .image_viewmodel import ImageViewModel
from .overlay_viewmodel import OverlayViewModel
from .toolbar_viewmodel import ToolbarViewModel
from ..services.pinnacle_service import PinnacleService

@dataclass
class MainViewModel:
    """Main orchestrator for all ViewModels"""

    # Injected service
    pinnacle_service: PinnacleService

    # Child ViewModels
    navigation_vm: NavigationViewModel = None
    image_vm: ImageViewModel = None
    overlay_vm: OverlayViewModel = None
    toolbar_vm: ToolbarViewModel = None

    def __post_init__(self):
        """Initialize child ViewModels and wire up callbacks"""
        # Initialize ViewModels
        self.navigation_vm = NavigationViewModel(
            pinnacle_service=self.pinnacle_service
        )
        self.image_vm = ImageViewModel()
        self.overlay_vm = OverlayViewModel()
        self.toolbar_vm = ToolbarViewModel()

        # Wire up callbacks
        self.navigation_vm.on_patient_changed = self._on_patient_changed
        self.navigation_vm.on_plan_changed = self._on_plan_changed
        self.navigation_vm.on_trial_changed = self._on_trial_changed

        self.overlay_vm.on_overlay_changed = self._on_overlay_changed

        self.toolbar_vm.on_data_loaded = self._on_data_loaded
        self.toolbar_vm.on_data_closed = self._on_data_closed

    def _on_patient_changed(self, patient_id: int):
        """Handle patient selection change"""
        # Update toolbar state
        self.toolbar_vm.has_data_loaded = True
        self.toolbar_vm.is_selection_complete = False

    def _on_plan_changed(self, plan_id: int):
        """Handle plan selection change"""
        # Load CT image for plan
        image_set = self.pinnacle_service.get_image_set(
            institution=1,
            patient=self.navigation_vm.selected_patient_id,
            image_set=0
        )
        self.image_vm.load_image_set(image_set)

        # Load ROIs and POIs for plan
        rois = self.pinnacle_service.get_rois(
            institution=1,
            patient=self.navigation_vm.selected_patient_id,
            plan=plan_id
        )
        self.overlay_vm.load_rois(rois)

        pois = self.pinnacle_service.get_points(
            institution=1,
            patient=self.navigation_vm.selected_patient_id,
            plan=plan_id
        )
        self.overlay_vm.load_pois(pois)

        # Update toolbar
        self.toolbar_vm.is_selection_complete = False

    def _on_trial_changed(self, trial_id: int):
        """Handle trial selection change"""
        # Load dose data
        dose = self.pinnacle_service.get_dose(
            institution=1,
            patient=self.navigation_vm.selected_patient_id,
            plan=self.navigation_vm.selected_plan_id,
            trial=trial_id
        )
        self.overlay_vm.load_dose(dose)

        # Load beams
        trial = self.navigation_vm.get_current_trial()
        self.overlay_vm.load_beams(trial.beam_list)

        # Update toolbar - selection now complete
        self.toolbar_vm.is_selection_complete = True

    def _on_overlay_changed(self):
        """Handle overlay state change"""
        # Regenerate SVG overlays
        self.image_vm.update_svg_overlays(self.overlay_vm)

    def _on_data_loaded(self):
        """Handle data source opened"""
        # Initialize Pinnacle service with data path
        self.navigation_vm.load_patients()

    def _on_data_closed(self):
        """Handle data source closed"""
        # Reset all ViewModels
        self.navigation_vm.reset()
        self.image_vm.reset()
        self.overlay_vm.reset()
        self.toolbar_vm.reset()
```

#### Task 8.2: Update main.py
- Create MainViewModel instance
- Pass to all view creation functions
- Set up app.storage initialization

**Main Application Structure**:
```python
# ui/main.py
from nicegui import ui, app
from .viewmodels.main_viewmodel import MainViewModel
from .services.pinnacle_service import PinnacleService
from .views import header_toolbar, left_drawer, ct_viewer, right_drawer
from .styles.theme import apply_dark_theme

# Initialize dark mode
dark = ui.dark_mode()
dark.enable()

# Apply custom dark theme
apply_dark_theme()

# Initialize services
pinnacle_service = PinnacleService()

# Create main ViewModel
main_vm = MainViewModel(pinnacle_service=pinnacle_service)

@ui.page('/')
def main_page():
    """Main application page"""

    # Header toolbar
    header_toolbar.create_header_toolbar(main_vm.toolbar_vm)

    # Main layout
    with ui.row().classes('w-full h-screen'):
        # Left drawer (navigation)
        left_drawer.create_left_drawer(main_vm.navigation_vm)

        # Center (CT viewer)
        ct_viewer.create_ct_viewer(main_vm.image_vm)

        # Right drawer (control panels)
        right_drawer.create_right_drawer(main_vm)

if __name__ in {'__main__', '__mp_main__'}:
    ui.run(
        title='Pinnacle DICOM Converter',
        favicon='🏥',
        dark=True,
        reload=True,  # Development mode
        show=True,     # Auto-open browser
        native=True    # Desktop mode
    )
```

**Deliverable**: Fully integrated MVVM application with reactive data flow

---

### Phase 9: Services & Display Models (Day 17)

**Objective**: Implement service layer and display models

#### Task 9.1: Pinnacle Service Wrapper
- Implement `services/pinnacle_service.py`
- Wrap PinnacleAPI with simplified interface
- Handle error cases and provide feedback

#### Task 9.2: Display Models
- Implement all display models in `models/` directory:
  - `patient_display.py`
  - `plan_display.py`
  - `trial_display.py`
  - `roi_display.py`
  - `poi_display.py`
  - `beam_display.py`
  - `dose_display.py`

**Example Display Model**:
```python
# models/roi_display.py
from dataclasses import dataclass
from typing import List, Tuple

@dataclass
class ROIDisplayModel:
    """Display model for ROI with UI-specific properties"""

    roi_id: int
    name: str
    color: str  # Hex color code
    visible: bool = True
    contours: List[List[Tuple[float, float, float]]] = None  # List of contour point lists

    # Additional UI-specific properties
    opacity: float = 1.0
    line_width: int = 2
```

**Deliverable**: Complete service layer and display models

---

### Phase 10: Dialogs & Utilities (Day 18)

**Objective**: Implement reusable dialogs and utility functions

#### Task 10.1: Help Dialog
- Implement help/about dialog showing keyboard shortcuts
- Display application version and information

**Help Dialog Specification**:
```python
# utils/dialogs.py
from nicegui import ui

def show_help_dialog():
    """Show help and keyboard shortcuts dialog"""
    with ui.dialog() as dialog, ui.card().classes('w-full max-w-2xl'):
        ui.label('Pinnacle DICOM Converter - Help').classes('text-h5 mb-4')

        # Keyboard shortcuts section
        ui.label('Keyboard Shortcuts').classes('text-h6 mt-4 mb-2')

        shortcuts = [
            ('Navigation', [
                ('↑ / ↓', 'Navigate slice by 1'),
                ('Page Up / Page Down', 'Navigate slice by 5'),
                ('Mouse Wheel', 'Navigate slices'),
            ]),
            ('Zoom', [
                ('Ctrl + Mouse Wheel', 'Zoom in/out'),
                ('+', 'Zoom in'),
                ('-', 'Zoom out'),
                ('Home', 'Reset zoom to 1.0x'),
            ]),
            ('View', [
                ('Ctrl + 1', 'Axial view'),
                ('Ctrl + 2', 'Sagittal view'),
                ('Ctrl + 3', 'Coronal view'),
            ]),
        ]

        for section, items in shortcuts:
            ui.label(section).classes('text-subtitle1 font-medium mt-3 mb-1')
            with ui.grid(columns=2).classes('gap-2'):
                for key, description in items:
                    ui.label(key).classes('font-mono text-sm bg-gray-700 px-2 py-1 rounded')
                    ui.label(description).classes('text-sm')

        # About section
        ui.separator().classes('my-4')
        ui.label('About').classes('text-h6 mb-2')
        ui.label('Pinnacle DICOM Converter v1.0.0').classes('text-sm')
        ui.label('Convert Pinnacle treatment planning data to DICOM format').classes('text-sm text-gray-400')

        # Close button
        with ui.row().classes('w-full justify-end mt-4'):
            ui.button('Close', on_click=dialog.close).props('flat')

    dialog.open()
```

#### Task 10.2: File Picker Dialog
- Implement directory picker using NiceGUI's local_file_picker
- Implement archive file picker (.tar, .tar.gz, .zip)

#### Task 10.3: Progress Dialog
- Create progress dialog for initial data loading
- Show progress bar and status messages

**Progress Dialog Specification**:
```python
# utils/dialogs.py
from nicegui import ui
from typing import Optional

class ProgressDialog:
    """Reusable progress dialog with status updates"""

    def __init__(self, title: str = 'Loading...'):
        self.dialog: Optional[ui.dialog] = None
        self.progress_bar: Optional[ui.linear_progress] = None
        self.status_label: Optional[ui.label] = None
        self.title = title

    def show(self):
        """Show the progress dialog"""
        with ui.dialog() as self.dialog, ui.card().classes('w-96'):
            ui.label(self.title).classes('text-h6 mb-4')

            self.status_label = ui.label('Initializing...').classes('text-sm mb-2')
            self.progress_bar = ui.linear_progress(value=0).classes('w-full')

        self.dialog.open()

    def update(self, progress: float, status: str):
        """Update progress and status"""
        if self.progress_bar:
            self.progress_bar.value = progress
        if self.status_label:
            self.status_label.text = status

    def close(self):
        """Close the dialog"""
        if self.dialog:
            self.dialog.close()

# Usage:
# progress = ProgressDialog('Loading Pinnacle Data')
# progress.show()
# progress.update(0.3, 'Loading patient data...')
# progress.update(0.6, 'Loading CT images...')
# progress.update(1.0, 'Complete!')
# progress.close()
```

**Deliverable**: Reusable dialog components

---

### Phase 11: Testing & Refinement (Days 19-20)

**Objective**: Test all functionality and refine user experience

#### Task 11.1: Manual Testing
- Test patient/plan/trial selection flow
- Test all orientation views (axial, sagittal, coronal)
- Test keyboard shortcuts
- Test window/level and zoom controls
- Test overlay visibility toggles
- Test ROI/POI color changes
- Test file open/close operations
- Test DICOM export (local and network)

#### Task 11.2: Performance Testing
- Test with large datasets (many slices, many ROIs)
- Measure CT image loading time
- Optimize SVG generation if needed
- Profile memory usage

#### Task 11.3: UI/UX Refinement
- Adjust spacing, padding, font sizes
- Fine-tune dark theme colors
- Add loading spinners where appropriate
- Improve error messages
- Add tooltips to all controls

#### Task 11.4: Bug Fixes
- Fix any issues discovered during testing
- Handle edge cases (empty data, missing files, etc.)

**Deliverable**: Polished, tested application ready for deployment

---

### Phase 12: Desktop Packaging (Day 21)

**Objective**: Package the application as a standalone desktop executable

#### Task 12.1: Configure for Native Mode
- Update main.py for native window mode
- Configure window size, icon, and title
- Test in native mode

#### Task 12.2: Package with PyInstaller
- Follow NiceGUI documentation for packaging
- Create spec file for PyInstaller
- Include all assets (icons, CSS files)
- Test executable on target platform

**Packaging Configuration**:
```python
# Create pyinstaller spec file
# pinnacle_dicom_converter.spec

from nicegui import ui

block_cipher = None

a = Analysis(
    ['src/pinnacle_dicom_converter/ui/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src/pinnacle_dicom_converter/ui/styles', 'styles'),
    ],
    hiddenimports=[
        'nicegui',
        'pandas',
        'numpy',
        'PIL',
        'pydicom',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PinnacleDicomConverter',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # No console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico',  # Application icon
)

# Build command:
# pyinstaller pinnacle_dicom_converter.spec
```

**Deliverable**: Standalone desktop executable

---

## Implementation Guidelines

### Code Organization Principles

1. **Separation of Concerns**: Keep Views, ViewModels, and Services strictly separated
2. **Dependency Injection**: Pass services and ViewModels explicitly, avoid global state
3. **Single Responsibility**: Each module should have one clear purpose
4. **Naming Conventions**:
   - Views: `create_<component_name>()`
   - ViewModels: `<Component>ViewModel`
   - Models: `<Entity>DisplayModel`
   - Services: `<Purpose>Service`

### Reactive Binding Strategy

**Use `@ui.refreshable` for**:
- Dynamic lists (AgGrid data that changes)
- Image viewer (when slice/orientation changes)
- Complex components that need complete re-render

**Use `bind_value()` for**:
- Two-way binding (sliders ↔ ViewModel properties)
- Form inputs that update state

**Use `bind_text_from()` for**:
- One-way display (labels showing ViewModel state)
- Read-only information displays

**Use `bind_enabled_from()` for**:
- Button/control enabled state based on conditions

### Performance Optimization

1. **Image Caching**: Cache base64-encoded images at ViewModel level to avoid re-encoding
2. **SVG Optimization**: Only regenerate SVG when overlay state or slice changes
3. **AgGrid Updates**: Use AgGrid's `update()` method instead of full refresh when possible
4. **Debouncing**: Debounce rapid slider changes to reduce update frequency

### Error Handling

1. **Service Layer**: Catch exceptions and return Result objects
2. **ViewModels**: Display user-friendly error messages via `ui.notify()`
3. **Views**: Show loading states and fallback content
4. **Logging**: Use Python logging for debugging

### Testing Strategy

1. **Manual Testing**: Focus on user workflows and edge cases
2. **Unit Tests**: Test ViewModels and Services (optional but recommended)
3. **Integration Tests**: Test data flow between components
4. **Performance Tests**: Measure loading times and responsiveness

---

## Technology Reference

### NiceGUI Documentation
- **Main Docs**: https://nicegui.io/documentation
- **Interactive Image**: https://nicegui.io/documentation/interactive_image
- **AgGrid**: https://nicegui.io/documentation/aggrid
- **Dark Mode**: https://nicegui.io/documentation/dark_mode
- **Storage**: https://nicegui.io/documentation/storage
- **Packaging**: https://nicegui.io/documentation/section_configuration_deployment#package_for_installation

### External Libraries
- **AgGrid Themes**: https://www.ag-grid.com/vue-data-grid/themes/
- **Tailwind CSS**: https://tailwindcss.com/docs
- **Quasar Components**: https://quasar.dev/vue-components
- **Material Icons**: https://fonts.google.com/icons

---

## Development Timeline

| Phase | Description | Duration | Dependencies |
|-------|-------------|----------|--------------|
| 1 | Foundation & Infrastructure | 2 days | - |
| 2 | Header Toolbar | 1 day | Phase 1 |
| 3 | Left Drawer Navigation | 2 days | Phase 1 |
| 4 | CT Viewer | 3 days | Phase 1 |
| 5 | Right Drawer Panels | 3 days | Phase 1, 4 |
| 6 | SVG Overlay System | 3 days | Phase 4, 5 |
| 7 | Overlay Control ViewModel | 1 day | Phase 6 |
| 8 | MVVM Integration | 2 days | All previous |
| 9 | Services & Models | 1 day | Phase 8 |
| 10 | Dialogs & Utilities | 1 day | Phase 8 |
| 11 | Testing & Refinement | 2 days | All previous |
| 12 | Desktop Packaging | 1 day | All previous |
| **Total** | | **21 days** | |

---

## Success Criteria

### Functional Requirements
- ✅ Open Pinnacle directories and archives (.tar, .tar.gz, .zip)
- ✅ Display patient, plan, and trial selection with AgGrid
- ✅ Show CT images in axial, sagittal, and coronal orientations
- ✅ Navigate slices with keyboard, mouse wheel, and slider
- ✅ Adjust window/level and zoom with sliders and inputs
- ✅ Display ROI, POI, beam, and isodose overlays as SVG
- ✅ Toggle overlay visibility and change colors
- ✅ Export DICOM files locally and to network
- ✅ Show help dialog with keyboard shortcuts
- ✅ Package as standalone desktop executable

### Non-Functional Requirements
- ✅ Dark theme matching reference screenshots
- ✅ Responsive, smooth interactions (<100ms for UI updates)
- ✅ Initial load complete within 5 seconds (progress shown)
- ✅ Professional, clean appearance
- ✅ Keyboard shortcuts functional
- ✅ Stable, no crashes during normal operation

---

## Future Enhancements (Out of Scope)

1. **3D Rendering**: Use `ui.scene()` for 3D visualization
2. **DVH Charts**: Dose-volume histogram display
3. **Plan Comparison**: Side-by-side plan comparison
4. **Annotation Tools**: Draw measurements on CT images
5. **Export Templates**: Customizable DICOM export configurations
6. **Multi-Language Support**: Internationalization
7. **Cloud Integration**: Save/load from cloud storage
8. **Collaborative Features**: Multi-user viewing/annotation

---

## Notes

- Keep the old tkinter UI in place during development for reference
- Use `guides/sample_ct_slice.png` for testing image display
- Reference the existing tkinter code for coordinate transformation logic
- Follow NiceGUI dev guide conventions for consistency
- Commit frequently with clear messages during development

---

## Questions for Clarification

If any requirements are unclear during implementation:

1. Coordinate transformations - refer to existing `ct_viewer.py:951` for `pixel_to_world_coordinates()`
2. SVG overlay positioning - use existing overlay panel logic as reference
3. DICOM export - reuse existing DicomExportManager
4. AgGrid configuration - experiment with dark themes and select best match

---

---

## Development Progress Tracker

**Instructions**:
- Mark tasks as complete by changing `[ ]` to `[x]`
- Add brief notes about the work completed after each task
- Include the date of completion in format: `(YYYY-MM-DD)`
- Update this checklist as you progress through the implementation

**Legend**:
- `[ ]` = Not started
- `[x]` = Completed
- Notes format: `Completed: Brief description of work. (YYYY-MM-DD)`

---

### Phase 1: Foundation & Infrastructure ✅

**Target Duration**: Days 1-2

- [x] **Task 1.1**: Project Setup
  Notes: Created complete directory structure for ui module with all subdirectories (views, viewmodels, models, converters, services, styles, utils). Implemented main.py with NiceGUI initialization, basic page layout with placeholder sections for all UI components. Added CLI command integration via `pinnacle-dicom-converter gui` with options for port, browser control, and reload mode. Added NiceGUI and pandas dependencies to pyproject.toml. (2025-09-30)

- [x] **Task 1.2**: Dark Theme Implementation
  Notes: Created styles/theme.py with comprehensive dark theme configuration matching "darkly" aesthetic. Implemented professional color palette with medical application focus (dark grays, blue primary, teal accent). Applied global CSS styling for all components (cards, drawers, headers, tables, inputs, scrollbars, tooltips). Created custom.css for component-specific styling including CT viewer, control panels, AgGrid customization, and responsive layouts. Theme successfully applied and tested via CLI launch. (2025-09-30)

**Phase 1 Complete**: ✅

---

### Phase 2: Header Toolbar ✅

**Target Duration**: Day 2

- [x] **Task 2.1**: Create Toolbar View
  Notes: Implemented views/header_toolbar.py with complete toolbar layout (title left, icon buttons/menus right). Styled with dark theme classes. All buttons properly bound to ViewModel state for enabled/disabled management. (2025-09-30)

- [x] **Task 2.2**: Create Toolbar ViewModel
  Notes: Implemented viewmodels/toolbar_viewmodel.py with all required properties (has_data_loaded, is_selection_complete) and methods (open_directory, open_archive, close_dataset, save_dicom_local, export_dicom_network, show_help). Placeholder implementations use ui.notify() for user feedback. Full integration with file dialogs and data services will occur in Phase 8. (2025-09-30)

**Phase 2 Complete**: ✅

---

### Phase 3: Left Drawer - Navigation ✅

**Target Duration**: Days 3-4

- [x] **Task 3.1**: Create Left Drawer View
  Notes: Implemented views/left_drawer.py with three @ui.refreshable AgGrid components (Patients, Plans, Trials). Each grid uses quartz-dark theme with single row selection. Grids are properly styled and integrated with NavigationViewModel. Created setup_refresh_callbacks() function to wire refresh methods. (2025-09-30)

- [x] **Task 3.2**: Create Navigation ViewModel
  Notes: Implemented viewmodels/navigation_viewmodel.py with complete state management for patient/plan/trial selection. Includes pandas DataFrames for AgGrid, selection handlers with cascading data loading, and callbacks to notify other ViewModels. Created load_mock_data() method with realistic test data: 3 patients, 8 plans total (2-3 per patient), 21 trials total (2-4 per plan). Selection events properly trigger data loading with proper parent-child relationships. (2025-09-30)

- [x] **Task 3.3**: Display Models (Patient/Plan/Trial)
  Notes: Created models/patient_display.py (patient_id, first_name, last_name, medical_record_number with full_name property), models/plan_display.py (plan_id, plan_name, patient_id), and models/trial_display.py (trial_id, trial_name, num_beams, plan_id). All models use @dataclass for clean implementation. Updated models/__init__.py with proper exports. (2025-09-30)

**Phase 3 Complete**: ✅

**Additional Implementation Notes**:
- Refactored main_page() logic into views/main_view.py for better organization
- Auto-load mock data on startup for easier testing during development
- Wired toolbar callbacks to navigation ViewModel for data loading/closing
- Export buttons properly enabled when trial selection is complete
- All three grids display correctly with cascading selection behavior

**Phase 5 Additional Implementation Notes**:
- Created comprehensive display models for ROI, POI, Beam, and Dose data
- Implemented ImageViewModel stub (full implementation pending Phase 4)
- Implemented OverlayViewModel with complete state management for all overlay types
- All panels use consistent card-based layouts with dark theme styling
- Mock data includes 5 ROIs, 2 POIs, 3 beams, and 7 isodose lines for realistic testing
- Expandable cards (ui.expansion) provide clean, space-efficient interface
- Real-time UI updates via refresh callbacks and ViewModel integration
- Color pickers and visibility toggles fully functional across all overlay types
- All controls properly validated and bound to ViewModels with two-way updates

---

### Phase 4: CT Viewer - Central Image Display ☐

**Target Duration**: Days 5-7

- [ ] **Task 4.1**: Create CT Viewer View
  Notes:

- [ ] **Task 4.2**: Create Image ViewModel
  Notes:

- [ ] **Task 4.3**: Image Service Implementation
  Notes:

**Phase 4 Complete**: ☐

---

### Phase 5: Right Drawer - Control Panels ✅

**Target Duration**: Days 8-10

- [x] **Task 5.1**: Create Right Drawer Container
  Notes: Implemented views/right_drawer.py with complete tabbed interface. Integrated current selection display card showing patient/plan/trial IDs with dynamic updates via callback wiring. Used ui.tabs() and ui.tab_panels() for clean organization of five control panels. Followed left drawer patterns for consistency. (2025-09-30)

- [x] **Task 5.2**: CT Control Panel
  Notes: Implemented views/panels/ct_panel.py with comprehensive CT image controls. Created sliders and number inputs for slice navigation (0-99), window width (1-4000), window level (-1024 to 3071), and zoom (0.1x-10.0x). All controls properly bound to ImageViewModel stub with real-time updates. Included reset button for zoom. Professional card-based layout with dark theme styling. (2025-09-30)

- [x] **Task 5.3**: ROI Control Panel
  Notes: Implemented views/panels/roi_panel.py with interactive ROI management. Used expandable ui.expansion cards for each ROI (5 mock ROIs loaded). Integrated visibility toggles (ui.switch), color pickers (ui.color_input), and ROI info display. Counter labels show total and visible ROI counts with dynamic updates. Properly wired to OverlayViewModel with refresh callbacks. (2025-09-30)

- [x] **Task 5.4**: POI Control Panel
  Notes: Implemented views/panels/poi_panel.py with POI controls. Similar structure to ROI panel with expandable cards for each POI (2 mock POIs loaded). Includes visibility toggles, color pickers, marker style selector (circle/cross/diamond/square), and coordinate display. Dynamic counter labels and refresh integration with OverlayViewModel. (2025-09-30)

- [x] **Task 5.5**: Beam Control Panel
  Notes: Implemented views/panels/beam_panel.py with beam visibility and information display. Expandable cards for each beam (3 mock beams loaded) with visibility toggles. Displays comprehensive beam parameters: ID, modality, energy, gantry angle, collimator angle, and couch angle. All properly formatted and styled. Integrated with OverlayViewModel refresh system. (2025-09-30)

- [x] **Task 5.6**: Dose Control Panel
  Notes: Implemented views/panels/dose_panel.py with dose control interface. Reference dose input field (default 3000 cGy) with proper validation. Isodose lines displayed as cards with color indicators, level labels (105%, 100%, 95%, etc.), and visibility toggles (7 mock isodose lines loaded). Shows calculated dose values in cGy based on reference dose. Dynamic counter labels with refresh integration. (2025-09-30)

**Phase 5 Complete**: ✅

---

### Phase 6: SVG Overlay System ☐

**Target Duration**: Days 11-13

- [ ] **Task 6.1**: SVG Converter Base
  Notes:

- [ ] **Task 6.2**: ROI to SVG Converter
  Notes:

- [ ] **Task 6.3**: POI to SVG Converter
  Notes:

- [ ] **Task 6.4**: Beam to SVG Converter
  Notes:

- [ ] **Task 6.5**: Isodose to SVG Converter
  Notes:

- [ ] **Task 6.6**: Integrate Overlays into CT Viewer
  Notes:

**Phase 6 Complete**: ☐

---

### Phase 7: Overlay Control ViewModel ☐

**Target Duration**: Day 14

- [ ] **Task 7.1**: Create Overlay ViewModel
  Notes:

**Phase 7 Complete**: ☐

---

### Phase 8: MVVM Integration & Data Flow ☐

**Target Duration**: Days 15-16

- [ ] **Task 8.1**: Create Main ViewModel
  Notes:

- [ ] **Task 8.2**: Update main.py with Full Integration
  Notes:

**Phase 8 Complete**: ☐

---

### Phase 9: Services & Display Models ☐

**Target Duration**: Day 17

- [ ] **Task 9.1**: Pinnacle Service Wrapper
  Notes:

- [ ] **Task 9.2**: Display Models Implementation
  - [ ] patient_display.py
  - [ ] plan_display.py
  - [ ] trial_display.py
  - [ ] roi_display.py
  - [ ] poi_display.py
  - [ ] beam_display.py
  - [ ] dose_display.py
  Notes:

**Phase 9 Complete**: ☐

---

### Phase 10: Dialogs & Utilities ☐

**Target Duration**: Day 18

- [ ] **Task 10.1**: Help Dialog
  Notes:

- [ ] **Task 10.2**: File Picker Dialog
  Notes:

- [ ] **Task 10.3**: Progress Dialog
  Notes:

**Phase 10 Complete**: ☐

---

### Phase 11: Testing & Refinement ☐

**Target Duration**: Days 19-20

- [ ] **Task 11.1**: Manual Testing
  - [ ] Patient/plan/trial selection flow
  - [ ] All orientation views (axial, sagittal, coronal)
  - [ ] Keyboard shortcuts
  - [ ] Window/level and zoom controls
  - [ ] Overlay visibility toggles
  - [ ] ROI/POI color changes
  - [ ] File open/close operations
  - [ ] DICOM export (local and network)
  Notes:

- [ ] **Task 11.2**: Performance Testing
  - [ ] Large dataset handling
  - [ ] CT image loading time measurement
  - [ ] SVG generation optimization
  - [ ] Memory usage profiling
  Notes:

- [ ] **Task 11.3**: UI/UX Refinement
  - [ ] Spacing and padding adjustments
  - [ ] Dark theme color fine-tuning
  - [ ] Loading spinners added
  - [ ] Error messages improved
  - [ ] Tooltips added to all controls
  Notes:

- [ ] **Task 11.4**: Bug Fixes
  Notes:

**Phase 11 Complete**: ☐

---

### Phase 12: Desktop Packaging ☐

**Target Duration**: Day 21

- [ ] **Task 12.1**: Configure for Native Mode
  Notes:

- [ ] **Task 12.2**: Package with PyInstaller
  Notes:

**Phase 12 Complete**: ☐

---

## Overall Project Status

**Start Date**: 2025-09-30

**Target Completion Date**: 2025-10-21 (21 days)

**Actual Completion Date**: _____________

**Total Progress**: 4 / 12 Phases Complete (33.3%)

**Phase Completion Summary**:
- Phase 1: Foundation & Infrastructure ✅ (2025-09-30)
- Phase 2: Header Toolbar ✅ (2025-09-30)
- Phase 3: Left Drawer Navigation ✅ (2025-09-30)
- Phase 4: CT Viewer - Central Image Display ☐ (Pending)
- Phase 5: Right Drawer - Control Panels ✅ (2025-09-30)

---

**End of Development Plan**