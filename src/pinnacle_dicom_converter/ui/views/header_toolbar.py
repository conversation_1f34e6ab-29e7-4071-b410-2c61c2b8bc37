"""Header toolbar view component."""

from nicegui import ui
from ..viewmodels.toolbar_viewmodel import ToolbarViewModel


def create_header_toolbar(toolbar_vm: ToolbarViewModel) -> None:
    """Create the header toolbar with icon buttons and menus.

    Implements the application's top toolbar with:
    - Application title on the left
    - File operations menu (folder icon)
    - DICOM export buttons (download, cloud upload)
    - Help button on the right

    All buttons are bound to the ViewModel's state for dynamic
    enabled/disabled behavior.

    Args:
        toolbar_vm: ToolbarViewModel instance managing toolbar state
    """
    with ui.header().classes('items-center justify-between p-2 shadow-md text-info'):
        # Left button group
        with ui.row().classes('items-center gap-1 flex-1'):
            # File menu (folder icon)
            with ui.button(icon='folder_open', color="info").props('flat round') as open_btn:
                open_btn.tooltip('Open Pinnacle Dataset')
                with ui.menu():
                    ui.menu_item(
                        'Open Directory...',
                        on_click=lambda: toolbar_vm.open_directory()
                    )
                    ui.menu_item(
                        'Open Archive (.tar, .tar.gz, .zip)...',
                        on_click=lambda: toolbar_vm.open_archive()
                    )
                    ui.separator()
                    close_item = ui.menu_item(
                        'Close Dataset',
                        on_click=lambda: toolbar_vm.close_dataset()
                    )
                    # Bind enabled state to ViewModel
                    close_item.bind_enabled_from(toolbar_vm, 'has_data_loaded')

            ui.separator().props('vertical inset').classes('q-separator--accent mx-2')

            # Download button (save locally)
            download_btn = ui.button(
                icon='download',
                color='info',
                on_click=lambda: toolbar_vm.save_dicom_local()
            ).props('flat round')
            download_btn.tooltip('Save DICOM Files Locally')
            download_btn.bind_enabled_from(toolbar_vm, 'is_selection_complete')

            # Export button (network) - using 'send' icon with right arrow
            export_btn = ui.button(
                icon='send',
                color='info',
                on_click=lambda: toolbar_vm.export_dicom_network()
            ).props('flat round')
            export_btn.tooltip('Send DICOM to Network')
            export_btn.bind_enabled_from(toolbar_vm, 'is_selection_complete')

            ui.separator().props('vertical inset').classes('q-separator--accent mx-2')

            # Help button
            ui.button(
                icon='help_outline',
                color='info',
                on_click=lambda: toolbar_vm.show_help()
            ).props('flat round').tooltip('Help & About')

        # Center: Application title
        ui.label('Pinnacle DICOM Converter').classes('text-h6 font-medium flex-1 text-center')

        # Right button group (pseudo-window controls)
        with ui.row().classes('items-center gap-1 flex-1 justify-end'):
            ui.button(icon='minimize', color='info').props('flat round').tooltip('Minimize')
            ui.button(icon='crop_square', color='info').props('flat round').tooltip('Maximize')
            ui.button(icon='close', color='info').props('flat round').tooltip('Close')