"""Right drawer view component for control panels."""

from nicegui import ui

from pinnacle_dicom_converter.ui.viewmodels.navigation_viewmodel import NavigationViewModel
from pinnacle_dicom_converter.ui.viewmodels.image_viewmodel import ImageViewModel
from pinnacle_dicom_converter.ui.viewmodels.overlay_viewmodel import OverlayViewModel
from pinnacle_dicom_converter.ui.views.panels import ct_panel, roi_panel, poi_panel, beam_panel, dose_panel


def create_right_drawer(
    nav_vm: NavigationViewModel,
    image_vm: ImageViewModel,
    overlay_vm: OverlayViewModel,
) -> None:
    """Create right drawer with control panels.

    Creates a right drawer containing:
    - Current selection information card
    - Tabbed control panels (CT, ROIs, POIs, Beams, Dose)

    Args:
        nav_vm: NavigationViewModel for displaying current selection
        image_vm: ImageViewModel for CT controls
        overlay_vm: OverlayViewModel for overlay controls
    """
    with ui.right_drawer(fixed=True).classes("bg_primary").props("width=400") as drawer:
        drawer.show()  # Always visible

        with ui.column().classes("w-full h-full"):
            # Current selection info card
            # with ui.card().classes("w-full bg_secondary mx-4 mt-4 mb-2"):
            with ui.column().classes("w-full"):
                ui.label("Trial Information").classes("text-h6 font-medium mb-2")

                # Patient selection
                patient_label = ui.label("Patient: -").classes("text-sm")

                def update_patient_label():
                    if nav_vm.selected_patient_id is not None:
                        patient_label.text = f"Patient: ID {nav_vm.selected_patient_id}"
                    else:
                        patient_label.text = "Patient: -"

                # Plan selection
                plan_label = ui.label("Plan: -").classes("text-sm")

                def update_plan_label():
                    if nav_vm.selected_plan_id is not None:
                        plan_label.text = f"Plan: ID {nav_vm.selected_plan_id}"
                    else:
                        plan_label.text = "Plan: -"

                # Trial selection
                trial_label = ui.label("Trial: -").classes("text-sm")

                def update_trial_label():
                    if nav_vm.selected_trial_id is not None:
                        trial_label.text = f"Trial: ID {nav_vm.selected_trial_id}"
                    else:
                        trial_label.text = "Trial: -"

                # Set up callbacks to update labels
                original_patient_callback = nav_vm.on_patient_changed
                original_plan_callback = nav_vm.on_plan_changed
                original_trial_callback = nav_vm.on_trial_changed

                def wrapped_patient_callback(patient_id):
                    update_patient_label()
                    update_plan_label()
                    update_trial_label()
                    if original_patient_callback:
                        original_patient_callback(patient_id)

                def wrapped_plan_callback(plan_id):
                    update_plan_label()
                    update_trial_label()
                    if original_plan_callback:
                        original_plan_callback(plan_id)

                def wrapped_trial_callback(trial_id):
                    update_trial_label()
                    if original_trial_callback:
                        original_trial_callback(trial_id)

                nav_vm.on_patient_changed = wrapped_patient_callback
                nav_vm.on_plan_changed = wrapped_plan_callback
                nav_vm.on_trial_changed = wrapped_trial_callback

                # Initial update
                update_patient_label()
                update_plan_label()
                update_trial_label()

            ui.separator()
            
            # Tabbed panels
            with ui.tabs().classes("w-full").props("dense") as tabs:
                ct_tab = ui.tab("CT", icon="image")
                roi_tab = ui.tab("ROIs", icon="polyline")
                poi_tab = ui.tab("POIs", icon="place")
                beam_tab = ui.tab("Beams", icon="straighten")
                dose_tab = ui.tab("Dose", icon="gradient")

            with ui.tab_panels(tabs, value=ct_tab).classes(
                "w-full flex-1 overflow-auto"
            ):
                with ui.tab_panel(ct_tab):
                    ct_panel.create_ct_panel(image_vm)

                with ui.tab_panel(roi_tab):
                    roi_panel.create_roi_panel(overlay_vm)

                with ui.tab_panel(poi_tab):
                    poi_panel.create_poi_panel(overlay_vm)

                with ui.tab_panel(beam_tab):
                    beam_panel.create_beam_panel(overlay_vm)

                with ui.tab_panel(dose_tab):
                    dose_panel.create_dose_panel(overlay_vm)


def setup_refresh_callbacks(overlay_vm: OverlayViewModel) -> None:
    """Set up refresh callbacks for all overlay panels.

    This connects the ViewModel's refresh methods to the UI refreshable functions.

    Args:
        overlay_vm: OverlayViewModel instance
    """
    roi_panel.setup_refresh_callback(overlay_vm)
    poi_panel.setup_refresh_callback(overlay_vm)
    beam_panel.setup_refresh_callback(overlay_vm)
    dose_panel.setup_refresh_callback(overlay_vm)
