"""Image ViewModel for CT display.

This ViewModel manages CT image display state including orientation, slice navigation,
window/level adjustments, and coordinate tracking. It provides the interface between
the UI components and the ImageService for data access.
"""

from typing import Optional, Callable

from pinnacle_dicom_converter.ui.services.image_service import ImageService


class ImageViewModel:
    """ViewModel for CT image display.

    Manages all aspects of CT image display including:
    - Image data and metadata
    - Display parameters (orientation, window/level, zoom)
    - Slice navigation
    - Coordinate tracking
    - SVG overlay content preparation
    - Keyboard shortcut handling

    Attributes:
        current_slice_index: Current slice being displayed (0-based)
        total_slices: Total number of slices in the image set
        window_width: CT window width for display
        window_level: CT window level (center) for display
        zoom_factor: Zoom level (1.0 = 100%)
        current_orientation: Current view orientation ('axial', 'sagittal', 'coronal')
        image_info_text: Descriptive text about current image
        coordinate_text: Current mouse coordinate display text
        current_image_url: Data URL for the current image
        svg_content: SVG overlay content for ROIs, POIs, etc.
    """

    def __init__(self, image_service: Optional[ImageService] = None):
        """Initialize the image ViewModel with default values.

        Args:
            image_service: Optional ImageService instance for data access
        """
        # Services
        self.image_service = image_service or ImageService()

        # Image data
        self.image_set: Optional[object] = None
        self.current_slice_index: int = 0
        self.total_slices: int = 0

        # Display parameters
        self.current_orientation: str = "axial"
        self.window_width: int = 1400
        self.window_level: int = 1000
        self.zoom_factor: float = 1.0

        # Image URL for ui.interactive_image
        self.current_image_url: Optional[str] = None

        # SVG overlay content
        self.svg_content: str = ""

        # Display text
        self.image_info_text: str = "No image loaded"
        self.coordinate_text: str = ""

        # Coordinate tracking
        self.pixel_spacing: tuple = (1.0, 1.0, 1.0)
        self.image_origin: tuple = (0.0, 0.0, 0.0)

        # Callbacks
        self.on_image_changed: Optional[Callable[[], None]] = None
        self._update_orientation_styles: Optional[Callable[[], None]] = None

    def load_image_set(self, image_set: Optional[object]) -> None:
        """Load CT image set.

        Args:
            image_set: ImageSet object from PinnacleAPI (None for placeholder)
        """
        self.image_set = image_set

        if image_set is not None:
            # Get image metadata from service
            dimensions = self.image_service.get_image_dimensions(image_set)
            self.total_slices = dimensions[2] if dimensions else 0
            self.current_slice_index = self.total_slices // 2  # Start at middle slice
            self.pixel_spacing = self.image_service.get_pixel_spacing(image_set)
            self.image_origin = self.image_service.get_image_origin(image_set)

            # Update info text
            self.image_info_text = (
                f"CT - {dimensions[0]}×{dimensions[1]}×{dimensions[2]} - "
                f"Spacing: {self.pixel_spacing[0]:.2f}×{self.pixel_spacing[1]:.2f}×{self.pixel_spacing[2]:.2f}mm"
            )
        else:
            # Load placeholder data
            self.total_slices = 100
            self.current_slice_index = 50
            self.pixel_spacing = (1.0, 1.0, 3.0)
            self.image_origin = (0.0, 0.0, 0.0)
            self.image_info_text = "Mock CT - 512×512×100 - Spacing: 1.0×1.0×3.0mm"

        # Generate initial image
        self.update_image_display()

    def load_mock_image_set(self) -> None:
        """Load mock image set for UI testing."""
        self.load_image_set(None)

    def update_image_display(self) -> None:
        """Update the displayed image based on current parameters."""
        if self.image_service:
            self.current_image_url = self.image_service.get_slice_data_url(
                self.image_set,
                self.current_slice_index,
                self.current_orientation,
                self.window_width,
                self.window_level
            )

        # Trigger UI refresh callback
        if hasattr(self, 'on_image_changed') and self.on_image_changed:
            self.on_image_changed()

    def set_orientation(self, orientation: str) -> None:
        """Change view orientation.

        Args:
            orientation: Target orientation ('axial', 'sagittal', 'coronal')
        """
        if orientation in ("axial", "sagittal", "coronal") and self.current_orientation != orientation:
            self.current_orientation = orientation
            self.update_image_display()

    def navigate_slice(self, delta: int) -> None:
        """Navigate to different slice by delta.

        Args:
            delta: Number of slices to move (positive or negative)
        """
        if self.total_slices > 0:
            new_index = max(0, min(self.current_slice_index + delta, self.total_slices - 1))
            if new_index != self.current_slice_index:
                self.current_slice_index = new_index
                self.update_image_display()

    def update_window_level(self) -> None:
        """Update image display after window/level changes."""
        self.update_image_display()

    def reset_zoom(self) -> None:
        """Reset zoom to 100%."""
        self.zoom_factor = 1.0
        self.update_image_display()

    def on_keyboard_event(self, key: str, action: str) -> None:
        """Handle keyboard shortcuts.

        Args:
            key: Key that was pressed
            action: Action type ('down', 'up', 'repeat')
        """
        if action != "down":
            return

        if key == "ArrowUp":
            self.navigate_slice(-1)
        elif key == "ArrowDown":
            self.navigate_slice(1)
        elif key == "PageUp":
            self.navigate_slice(-5)
        elif key == "PageDown":
            self.navigate_slice(5)
        elif key == "Home":
            self.reset_zoom()
        elif key == "Equal" or key == "Plus":  # + key for zoom in
            self.zoom_factor = min(10.0, self.zoom_factor + 0.1)
            self.update_image_display()
        elif key == "Minus":  # - key for zoom out
            self.zoom_factor = max(0.1, self.zoom_factor - 0.1)
            self.update_image_display()

    def on_mouse_move(self, image_x: float, image_y: float) -> None:
        """Update coordinate display on mouse move.

        Args:
            image_x: X coordinate in image pixels
            image_y: Y coordinate in image pixels
        """
        if self.image_service:
            world_coords = self.image_service.pixel_to_world_coordinates(
                image_x, image_y, self.current_slice_index, self.image_set, self.current_orientation
            )
            if world_coords:
                x, y, z = world_coords
                self.coordinate_text = f"({x:.1f}, {y:.1f}, {z:.1f})"

    def update_svg_overlays(self, overlay_vm=None) -> None:
        """Generate SVG overlay content from overlay ViewModel.

        Args:
            overlay_vm: OverlayViewModel instance with ROI/POI/Beam/Dose data
        """
        # For Phase 4, just prepare empty SVG content
        # Full SVG overlay implementation will be added in Phase 6
        if overlay_vm is None:
            self.svg_content = ""
            return

        # Future implementation will generate SVG content here
        # based on visible ROIs, POIs, beams, and isodose lines
        self.svg_content = ""

    def reset(self) -> None:
        """Reset all state to initial values."""
        self.image_set = None
        self.current_slice_index = 0
        self.total_slices = 0
        self.current_orientation = "axial"
        self.window_width = 1400
        self.window_level = 1000
        self.zoom_factor = 1.0
        self.current_image_url = None
        self.svg_content = ""
        self.image_info_text = "No image loaded"
        self.coordinate_text = ""
        self.pixel_spacing = (1.0, 1.0, 1.0)
        self.image_origin = (0.0, 0.0, 0.0)
