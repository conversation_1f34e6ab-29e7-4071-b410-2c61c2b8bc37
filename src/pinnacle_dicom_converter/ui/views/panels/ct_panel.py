"""CT control panel for image display controls."""

from nicegui import ui

from ...viewmodels.image_viewmodel import ImageViewModel


def create_ct_panel(image_vm: ImageViewModel) -> None:
    """Create CT control panel with image display controls.

    Provides controls for:
    - Slice navigation
    - Window width and level adjustment
    - Zoom control

    Args:
        image_vm: ImageViewModel instance managing CT display state
    """
    with ui.column().classes("w-full gap-4 p-4"):
        ui.label("CT Image Controls").classes("text-h6 font-medium mb-2")

        # Image information card
        with ui.card().classes("w-full bg_secondary p-3"):
            ui.label("Image Information").classes("text-subtitle2 font-medium mb-2")
            info_text = ui.label("").classes("text-xs text-gray-300")
            info_text.bind_text_from(image_vm, "image_info_text")

        # Slice control
        with ui.card().classes("w-full bg_secondary p-3"):
            ui.label("Slice Navigation").classes("text-subtitle2 font-medium mb-2")

            with ui.row().classes("w-full items-center gap-2"):
                slice_slider = ui.slider(
                    min=0, max=max(image_vm.total_slices - 1, 0), step=1, value=image_vm.current_slice_index
                ).classes("flex-1")

                slice_input = ui.number(
                    min=0,
                    max=max(image_vm.total_slices - 1, 0),
                    step=1,
                    value=image_vm.current_slice_index,
                ).classes("w-20").props("dense outlined")

            # Slice info
            slice_info = ui.label(
                f"Slice: {image_vm.current_slice_index + 1} / {image_vm.total_slices}"
            ).classes("text-xs text-gray-400 mt-1")

            # Update handlers
            def on_slice_change(e):
                new_index = int(e.value)
                if new_index != image_vm.current_slice_index:
                    image_vm.current_slice_index = new_index
                    image_vm.update_image_display()
                    slice_input.value = image_vm.current_slice_index
                    slice_info.text = (
                        f"Slice: {image_vm.current_slice_index + 1} / {image_vm.total_slices}"
                    )

            slice_slider.on("update:model-value", on_slice_change)

            def on_slice_input_change():
                new_index = int(slice_input.value)
                if new_index != image_vm.current_slice_index:
                    image_vm.current_slice_index = new_index
                    image_vm.update_image_display()
                    slice_slider.value = image_vm.current_slice_index
                    slice_info.text = (
                        f"Slice: {image_vm.current_slice_index + 1} / {image_vm.total_slices}"
                    )

            slice_input.on("update:model-value", on_slice_input_change)

        # Window Width control
        with ui.card().classes("w-full bg_secondary p-3"):
            ui.label("Window Width").classes("text-subtitle2 font-medium mb-2")

            with ui.row().classes("w-full items-center gap-2"):
                ww_slider = ui.slider(
                    min=1, max=4000, step=10, value=image_vm.window_width
                ).classes("flex-1")

                ww_input = ui.number(
                    min=1, max=4000, step=10, value=image_vm.window_width
                ).classes("w-24").props("dense outlined")

            # Update handlers
            def on_ww_change(e):
                new_width = int(e.value)
                if new_width != image_vm.window_width:
                    image_vm.window_width = new_width
                    image_vm.update_window_level()
                    ww_input.value = image_vm.window_width

            ww_slider.on("update:model-value", on_ww_change)

            def on_ww_input_change():
                new_width = int(ww_input.value)
                if new_width != image_vm.window_width:
                    image_vm.window_width = new_width
                    image_vm.update_window_level()
                    ww_slider.value = image_vm.window_width

            ww_input.on("update:model-value", on_ww_input_change)

        # Window Level control
        with ui.card().classes("w-full bg_secondary p-3"):
            ui.label("Window Level").classes("text-subtitle2 font-medium mb-2")

            with ui.row().classes("w-full items-center gap-2"):
                wl_slider = ui.slider(
                    min=-1024, max=3071, step=10, value=image_vm.window_level
                ).classes("flex-1")

                wl_input = ui.number(
                    min=-1024, max=3071, step=10, value=image_vm.window_level
                ).classes("w-24").props("dense outlined")

            # Update handlers
            def on_wl_change(e):
                new_level = int(e.value)
                if new_level != image_vm.window_level:
                    image_vm.window_level = new_level
                    image_vm.update_window_level()
                    wl_input.value = image_vm.window_level

            wl_slider.on("update:model-value", on_wl_change)

            def on_wl_input_change():
                new_level = int(wl_input.value)
                if new_level != image_vm.window_level:
                    image_vm.window_level = new_level
                    image_vm.update_window_level()
                    wl_slider.value = image_vm.window_level

            wl_input.on("update:model-value", on_wl_input_change)

        # Zoom control
        with ui.card().classes("w-full bg_secondary p-3"):
            ui.label("Zoom").classes("text-subtitle2 font-medium mb-2")

            with ui.row().classes("w-full items-center gap-2"):
                zoom_slider = ui.slider(
                    min=0.1, max=10.0, step=0.1, value=image_vm.zoom_factor
                ).classes("flex-1")

                zoom_input = ui.number(
                    min=0.1,
                    max=10.0,
                    step=0.1,
                    value=image_vm.zoom_factor,
                    format="%.1fx",
                ).classes("w-24").props("dense outlined")

                # Reset button
                ui.button("Reset", on_click=lambda: reset_zoom()).props(
                    "flat dense"
                ).classes("ml-2")

            # Update handlers
            def on_zoom_change(e):
                new_zoom = float(e.value)
                if new_zoom != image_vm.zoom_factor:
                    image_vm.zoom_factor = new_zoom
                    image_vm.update_image_display()
                    zoom_input.value = image_vm.zoom_factor

            zoom_slider.on("update:model-value", on_zoom_change)

            def on_zoom_input_change():
                new_zoom = float(zoom_input.value)
                if new_zoom != image_vm.zoom_factor:
                    image_vm.zoom_factor = new_zoom
                    image_vm.update_image_display()
                    zoom_slider.value = image_vm.zoom_factor

            zoom_input.on("update:model-value", on_zoom_input_change)

            def reset_zoom():
                image_vm.reset_zoom()
                zoom_slider.value = image_vm.zoom_factor
                zoom_input.value = image_vm.zoom_factor
